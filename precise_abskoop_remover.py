#!/usr/bin/env python3
"""
精确的 abskoop 水印检测和去除工具
只检测和去除 www.abskoop.com 水印，保护其他文字内容
"""

import cv2
import numpy as np
import os
import argparse
from pathlib import Path
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PreciseAbskoopRemover:
    def __init__(self):
        self.supported_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
        # abskoop水印的特征
        self.target_text = "www.abskoop.com"
        self.watermark_keywords = ["abskoop", "www.abskoop", "abskoop.com"]
    
    def detect_abskoop_watermark_precise(self, image):
        """
        精确检测 www.abskoop.com 水印
        避免误检其他文字内容
        """
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        height, width = gray.shape
        
        logger.info("开始精确检测 www.abskoop.com 水印...")
        
        # 方法1: 特定亮度范围检测 (abskoop水印通常在这个范围)
        mask1 = self._detect_watermark_brightness(gray)
        
        # 方法2: 检测重复模式 (水印通常是重复的)
        mask2 = self._detect_repetitive_pattern(gray)
        
        # 方法3: 检测半透明特征
        mask3 = self._detect_semitransparent_text(gray)
        
        # 方法4: 基于位置的过滤 (水印通常分布在特定位置)
        mask4 = self._detect_by_position_pattern(gray)
        
        # 智能组合 - 只有多个方法都检测到才认为是水印
        combined_mask = self._precise_combine_masks([mask1, mask2, mask3, mask4])
        
        # 精确后处理 - 去除可能的正常文字
        final_mask = self._filter_normal_text(combined_mask, gray)
        
        logger.info("精确水印检测完成")
        return final_mask
    
    def _detect_watermark_brightness(self, gray):
        """检测水印特有的亮度范围"""
        # abskoop水印通常在180-220的亮度范围，且比较均匀
        mask = cv2.inRange(gray, 185, 215)
        
        # 去除太大的区域（可能是背景）
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        filtered_mask = np.zeros_like(gray)
        
        for contour in contours:
            area = cv2.contourArea(contour)
            # 水印文字的面积范围
            if 50 < area < 3000:
                cv2.fillPoly(filtered_mask, [contour], 255)
        
        return filtered_mask
    
    def _detect_repetitive_pattern(self, gray):
        """检测重复模式 - 水印通常是重复出现的"""
        height, width = gray.shape
        
        # 将图像分成网格，检查相似的区域
        grid_h, grid_w = height // 6, width // 8
        patterns = []
        
        # 提取可能的水印模板
        for y in range(0, height - grid_h, grid_h):
            for x in range(0, width - grid_w, grid_w):
                template = gray[y:y+grid_h, x:x+grid_w]
                
                # 检查这个区域是否包含类似水印的特征
                mean_val = np.mean(template)
                std_val = np.std(template)
                
                # 水印区域通常有适中的亮度和较低的对比度
                if 180 < mean_val < 220 and 10 < std_val < 40:
                    patterns.append((template, x, y))
        
        # 寻找重复的模式
        mask = np.zeros_like(gray)
        
        for i, (template1, x1, y1) in enumerate(patterns):
            matches = 0
            for j, (template2, x2, y2) in enumerate(patterns):
                if i != j:
                    # 计算模板相似度
                    if template1.shape == template2.shape:
                        correlation = cv2.matchTemplate(template1, template2, cv2.TM_CCOEFF_NORMED)
                        if correlation[0, 0] > 0.7:  # 高相似度
                            matches += 1
            
            # 如果找到多个相似的模式，认为是水印
            if matches >= 2:
                mask[y1:y1+grid_h, x1:x1+grid_w] = 255
        
        return mask
    
    def _detect_semitransparent_text(self, gray):
        """检测半透明文字特征"""
        # 使用边缘检测找到文字轮廓
        edges = cv2.Canny(gray, 30, 80)
        
        # 膨胀边缘以连接文字
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
        edges_dilated = cv2.dilate(edges, kernel, iterations=2)
        
        # 查找轮廓
        contours, _ = cv2.findContours(edges_dilated, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        mask = np.zeros_like(gray)
        
        for contour in contours:
            area = cv2.contourArea(contour)
            x, y, w, h = cv2.boundingRect(contour)
            aspect_ratio = w / h if h > 0 else 0
            
            # 检查区域内的亮度特征
            roi = gray[y:y+h, x:x+w]
            mean_brightness = np.mean(roi)
            
            # 水印文字的特征：
            # 1. 适中的面积
            # 2. 合理的宽高比
            # 3. 特定的亮度范围
            if (100 < area < 2000 and 
                1 < aspect_ratio < 8 and 
                180 < mean_brightness < 220):
                cv2.fillPoly(mask, [contour], 255)
        
        return mask
    
    def _detect_by_position_pattern(self, gray):
        """基于位置模式检测 - 水印通常有规律的分布"""
        height, width = gray.shape
        mask = np.zeros_like(gray)
        
        # 检查是否有规律分布的文字区域
        # 水印通常每隔一定距离重复出现
        
        # 使用自适应阈值检测所有文字
        adaptive_thresh = cv2.adaptiveThreshold(
            gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
        )
        
        contours, _ = cv2.findContours(adaptive_thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # 收集所有文字区域的中心点
        text_centers = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if 50 < area < 2000:  # 合理的文字大小
                M = cv2.moments(contour)
                if M["m00"] != 0:
                    cx = int(M["m10"] / M["m00"])
                    cy = int(M["m01"] / M["m00"])
                    text_centers.append((cx, cy, contour))
        
        # 检查是否有规律的间距
        regular_patterns = []
        for i, (cx1, cy1, contour1) in enumerate(text_centers):
            pattern_count = 1
            for j, (cx2, cy2, contour2) in enumerate(text_centers):
                if i != j:
                    # 检查是否在预期的水印间距内
                    dx, dy = abs(cx2 - cx1), abs(cy2 - cy1)
                    # 水印通常间距在150-300像素之间
                    if (100 < dx < 400 and dy < 100) or (100 < dy < 400 and dx < 100):
                        pattern_count += 1
            
            # 如果找到规律分布，标记为水印
            if pattern_count >= 3:
                regular_patterns.append(contour1)
        
        # 绘制检测到的规律分布区域
        for contour in regular_patterns:
            cv2.fillPoly(mask, [contour], 255)
        
        return mask
    
    def _precise_combine_masks(self, masks):
        """精确组合遮罩 - 只有多个方法都检测到才认为是水印"""
        if not masks:
            return np.zeros((100, 100), dtype=np.uint8)
        
        # 计算每个像素被多少个方法检测到
        vote_map = np.zeros_like(masks[0], dtype=np.float32)
        
        for mask in masks:
            vote_map += (mask > 0).astype(np.float32)
        
        # 只有被至少2个方法检测到的区域才认为是水印
        threshold = 2
        final_mask = (vote_map >= threshold).astype(np.uint8) * 255
        
        return final_mask
    
    def _filter_normal_text(self, mask, gray):
        """过滤掉可能的正常文字，只保留水印"""
        # 分析每个连通区域
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        filtered_mask = np.zeros_like(gray)
        
        for contour in contours:
            area = cv2.contourArea(contour)
            x, y, w, h = cv2.boundingRect(contour)
            
            # 提取区域
            roi = gray[y:y+h, x:x+w]
            
            # 分析区域特征
            mean_brightness = np.mean(roi)
            std_brightness = np.std(roi)
            aspect_ratio = w / h if h > 0 else 0
            
            # 水印的特征判断
            is_watermark = (
                # 亮度特征
                180 < mean_brightness < 220 and
                # 对比度特征 (水印通常对比度较低)
                std_brightness < 30 and
                # 大小特征
                100 < area < 3000 and
                # 宽高比特征
                1 < aspect_ratio < 10
            )
            
            if is_watermark:
                cv2.fillPoly(filtered_mask, [contour], 255)
        
        # 最终清理
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        filtered_mask = cv2.morphologyEx(filtered_mask, cv2.MORPH_OPEN, kernel, iterations=1)
        filtered_mask = cv2.morphologyEx(filtered_mask, cv2.MORPH_CLOSE, kernel, iterations=2)
        
        return filtered_mask
    
    def remove_watermark_carefully(self, image, mask):
        """小心地去除水印，保护其他内容"""
        logger.info("开始小心去除水印...")
        
        # 检查遮罩大小
        mask_area = np.sum(mask == 255)
        total_area = mask.shape[0] * mask.shape[1]
        mask_ratio = mask_area / total_area
        
        logger.info(f"将要修复的区域占比: {mask_ratio:.2%}")
        
        if mask_ratio > 0.1:
            logger.warning("检测到的区域较大，可能包含正常内容，建议检查遮罩")
        
        # 使用保守的修复参数
        if mask_ratio < 0.02:
            # 小区域 - 精细修复
            result = cv2.inpaint(image, mask, 3, cv2.INPAINT_TELEA)
        else:
            # 较大区域 - 使用更大半径但保守的方法
            result = cv2.inpaint(image, mask, 5, cv2.INPAINT_TELEA)
        
        logger.info("小心水印去除完成")
        return result
    
    def process_single_image(self, input_path, output_path, save_debug=False):
        """处理单张图片"""
        try:
            logger.info(f"开始精确处理: {input_path}")
            
            # 读取图片
            image = cv2.imread(str(input_path))
            if image is None:
                logger.error(f"无法读取图片: {input_path}")
                return False
            
            logger.info(f"图片尺寸: {image.shape}")
            
            # 精确检测水印
            mask = self.detect_abskoop_watermark_precise(image)
            
            # 检查遮罩质量
            mask_area = np.sum(mask == 255)
            total_area = mask.shape[0] * mask.shape[1]
            mask_ratio = mask_area / total_area
            
            logger.info(f"检测到的水印区域占比: {mask_ratio:.2%}")
            
            if mask_ratio < 0.001:
                logger.warning("检测到的水印区域很小，可能没有水印或检测不准确")
            elif mask_ratio > 0.05:
                logger.warning("检测到的水印区域较大，请检查是否误检了正常内容")
            
            # 保存调试信息
            if save_debug:
                debug_dir = output_path.parent / "debug"
                debug_dir.mkdir(exist_ok=True)
                
                mask_path = debug_dir / f"{output_path.stem}_precise_mask.png"
                cv2.imwrite(str(mask_path), mask)
                logger.info(f"精确遮罩已保存: {mask_path}")
            
            # 小心去除水印
            result = self.remove_watermark_carefully(image, mask)
            
            # 保存结果
            cv2.imwrite(str(output_path), result)
            logger.info(f"精确处理完成: {output_path}")
            
            return True
            
        except Exception as e:
            logger.error(f"处理图片时出错 {input_path}: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

def main():
    parser = argparse.ArgumentParser(description='精确的abskoop水印去除工具')
    parser.add_argument('--input', '-i', required=True, help='输入图片文件')
    parser.add_argument('--output', '-o', required=True, help='输出文件')
    parser.add_argument('--debug', action='store_true', help='保存调试信息')
    
    args = parser.parse_args()
    
    remover = PreciseAbskoopRemover()
    
    input_path = Path(args.input)
    output_path = Path(args.output)
    
    logger.info("🎯 精确abskoop水印去除工具启动")
    logger.info("=" * 50)
    
    if input_path.is_file():
        # 处理单个文件
        remover.process_single_image(input_path, output_path, args.debug)
    else:
        logger.error(f"输入路径不存在: {input_path}")

if __name__ == "__main__":
    main()
