#!/usr/bin/env python3
"""
可视化水印检测工具
生成不同检测方法的可视化结果，帮助理解和调试
"""

import cv2
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import argparse

class VisualWatermarkDetector:
    def __init__(self):
        pass
    
    def detect_and_visualize(self, image_path, output_dir):
        """检测并可视化不同方法的结果"""
        # 读取图片
        image = cv2.imread(str(image_path))
        if image is None:
            print(f"❌ 无法读取图片: {image_path}")
            return
        
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        height, width = gray.shape
        
        print(f"📷 处理图片: {Path(image_path).name}")
        print(f"   尺寸: {width} x {height}")
        print(f"   平均亮度: {np.mean(gray):.1f}")
        
        # 创建输出目录
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        # 不同的检测方法
        methods = self._apply_detection_methods(gray)
        
        # 创建可视化
        self._create_visualization(image, gray, methods, output_path, Path(image_path).stem)
        
        # 生成不同强度的检测结果
        self._generate_different_intensities(image, gray, output_path, Path(image_path).stem)
        
        print(f"✅ 可视化结果已保存到: {output_path}")
    
    def _apply_detection_methods(self, gray):
        """应用不同的检测方法"""
        methods = {}
        
        # 1. 不同亮度范围
        methods['亮度180-220'] = cv2.inRange(gray, 180, 220)
        methods['亮度190-230'] = cv2.inRange(gray, 190, 230)
        methods['亮度200-240'] = cv2.inRange(gray, 200, 240)
        methods['亮度170-210'] = cv2.inRange(gray, 170, 210)
        
        # 2. 边缘检测
        methods['Canny边缘'] = cv2.Canny(gray, 20, 60)
        methods['Canny敏感'] = cv2.Canny(gray, 10, 40)
        
        # 3. 自适应阈值
        methods['自适应阈值'] = cv2.adaptiveThreshold(
            gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
        )
        
        # 4. 全局阈值
        _, methods['全局阈值'] = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        # 计算每种方法的检测比例
        total_pixels = gray.shape[0] * gray.shape[1]
        for name, mask in methods.items():
            detection_ratio = np.sum(mask == 255) / total_pixels
            print(f"   {name}: {detection_ratio:.3%}")
        
        return methods
    
    def _create_visualization(self, image, gray, methods, output_path, filename):
        """创建检测方法的可视化"""
        # 计算网格大小
        num_methods = len(methods) + 2  # +2 for original and gray
        cols = 4
        rows = (num_methods + cols - 1) // cols
        
        fig, axes = plt.subplots(rows, cols, figsize=(20, 5 * rows))
        if rows == 1:
            axes = [axes] if cols == 1 else axes
        else:
            axes = axes.flatten()
        
        # 原图
        axes[0].imshow(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
        axes[0].set_title('原始图片', fontsize=12)
        axes[0].axis('off')
        
        # 灰度图
        axes[1].imshow(gray, cmap='gray')
        axes[1].set_title('灰度图', fontsize=12)
        axes[1].axis('off')
        
        # 各种检测方法
        for i, (name, mask) in enumerate(methods.items(), 2):
            if i < len(axes):
                axes[i].imshow(mask, cmap='gray')
                
                # 计算检测比例
                detection_ratio = np.sum(mask == 255) / (mask.shape[0] * mask.shape[1])
                title = f'{name}\n({detection_ratio:.3%})'
                axes[i].set_title(title, fontsize=10)
                axes[i].axis('off')
        
        # 隐藏多余的子图
        for i in range(num_methods, len(axes)):
            axes[i].axis('off')
        
        plt.tight_layout()
        plt.savefig(output_path / f"{filename}_detection_methods.png", dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"   检测方法可视化: {filename}_detection_methods.png")
    
    def _generate_different_intensities(self, image, gray, output_path, filename):
        """生成不同检测强度的结果"""
        # 定义不同的检测强度
        intensities = [
            ('宽松', {'brightness': (160, 240), 'canny': (10, 30), 'vote_threshold': 0.2}),
            ('中等', {'brightness': (180, 220), 'canny': (20, 60), 'vote_threshold': 0.3}),
            ('严格', {'brightness': (190, 210), 'canny': (30, 80), 'vote_threshold': 0.5}),
            ('极严格', {'brightness': (195, 215), 'canny': (40, 100), 'vote_threshold': 0.7}),
        ]
        
        fig, axes = plt.subplots(2, 4, figsize=(20, 10))
        
        for i, (name, params) in enumerate(intensities):
            # 应用检测
            mask = self._apply_intensity_detection(gray, params)
            
            # 计算统计
            detection_ratio = np.sum(mask == 255) / (mask.shape[0] * mask.shape[1])
            
            # 显示遮罩
            axes[0, i].imshow(mask, cmap='gray')
            axes[0, i].set_title(f'{name}检测\n({detection_ratio:.3%})', fontsize=12)
            axes[0, i].axis('off')
            
            # 显示应用效果（如果有检测到的区域）
            if detection_ratio > 0:
                # 应用修复
                result = cv2.inpaint(image, mask, 3, cv2.INPAINT_TELEA)
                axes[1, i].imshow(cv2.cvtColor(result, cv2.COLOR_BGR2RGB))
                axes[1, i].set_title(f'{name}修复结果', fontsize=12)
            else:
                axes[1, i].imshow(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
                axes[1, i].set_title(f'{name}修复结果\n(无检测)', fontsize=12)
            axes[1, i].axis('off')
            
            # 保存单独的结果
            if detection_ratio > 0:
                result_path = output_path / f"{filename}_{name}_removed.jpg"
                cv2.imwrite(str(result_path), result)
                print(f"   {name}检测结果: {filename}_{name}_removed.jpg ({detection_ratio:.3%})")
        
        plt.tight_layout()
        plt.savefig(output_path / f"{filename}_intensity_comparison.png", dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"   强度对比图: {filename}_intensity_comparison.png")
    
    def _apply_intensity_detection(self, gray, params):
        """应用特定强度的检测"""
        # 亮度检测
        brightness_low, brightness_high = params['brightness']
        mask1 = cv2.inRange(gray, brightness_low, brightness_high)
        
        # 边缘检测
        canny_low, canny_high = params['canny']
        edges = cv2.Canny(gray, canny_low, canny_high)
        
        # 文字检测
        adaptive_thresh = cv2.adaptiveThreshold(
            gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
        )
        
        # 投票组合
        vote_map = np.zeros_like(gray, dtype=np.float32)
        vote_map += (mask1 > 0).astype(np.float32) * 0.4
        vote_map += (edges > 0).astype(np.float32) * 0.3
        vote_map += (adaptive_thresh == 0).astype(np.float32) * 0.3  # 注意这里是反向的
        
        # 应用投票阈值
        vote_threshold = params['vote_threshold']
        final_mask = (vote_map >= vote_threshold).astype(np.uint8) * 255
        
        # 后处理
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        final_mask = cv2.morphologyEx(final_mask, cv2.MORPH_OPEN, kernel, iterations=1)
        final_mask = cv2.morphologyEx(final_mask, cv2.MORPH_CLOSE, kernel, iterations=2)
        
        return final_mask

def main():
    parser = argparse.ArgumentParser(description='可视化水印检测工具')
    parser.add_argument('--input', '-i', required=True, help='输入图片文件')
    parser.add_argument('--output', '-o', default='visual_detection_results', help='输出目录')
    
    args = parser.parse_args()
    
    detector = VisualWatermarkDetector()
    
    print("👁️ 可视化水印检测工具")
    print("=" * 50)
    
    detector.detect_and_visualize(args.input, args.output)
    
    print("\n📋 生成的文件:")
    print("1. *_detection_methods.png - 不同检测方法对比")
    print("2. *_intensity_comparison.png - 不同强度检测对比")
    print("3. *_宽松_removed.jpg - 宽松检测的修复结果")
    print("4. *_中等_removed.jpg - 中等检测的修复结果")
    print("5. 等等...")
    
    print("\n💡 建议:")
    print("- 查看可视化图片，选择检测效果最好的强度")
    print("- 如果所有检测都太少，可能图片中没有明显的水印")
    print("- 如果检测太多，说明误检了正常内容")

if __name__ == "__main__":
    main()
