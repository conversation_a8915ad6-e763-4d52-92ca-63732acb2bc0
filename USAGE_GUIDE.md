# 🎯 水印去除工具使用指南

## ✅ 环境已就绪

你的水印去除环境已经完全设置好了！包含：

- ✅ Python虚拟环境 (`lama_env`)
- ✅ 所需依赖包 (OpenCV, NumPy, Pillow)
- ✅ 水印去除工具 (`watermark_remover.py`)
- ✅ 测试示例已生成

## 🚀 快速开始

### 1. 激活环境
```bash
source lama_env/bin/activate
```

### 2. 放置你的图片
将需要去除水印的图片放入 `input_images/` 目录

### 3. 运行批量处理
```bash
python watermark_remover.py -i input_images -o output_images --mask-method text
```

## 📁 目录结构
```
remove_watermark/
├── lama_env/                          # Python虚拟环境
├── input_images/                      # 📥 放置原始图片
│   └── test_with_watermark.jpg       # 测试图片（已生成）
├── output_images/                     # 📤 处理后的图片
│   ├── removed_*.jpg                 # 去除水印后的图片
│   └── *_mask.jpg                    # 生成的遮罩（调试用）
├── watermark_remover.py              # 🔧 主工具
├── test_watermark_removal.py         # 🧪 测试脚本
└── README.md                         # 📖 详细说明
```

## 🎛️ 命令选项

### 基本用法
```bash
# 处理单张图片
python watermark_remover.py -i input.jpg -o output.jpg

# 批量处理目录
python watermark_remover.py -i input_images -o output_images
```

### 高级选项
```bash
# 文字水印专用模式（推荐用于 www.abskoop.com 类型水印）
--mask-method text

# 自动检测模式（默认）
--mask-method auto

# 修复算法选择
--inpaint-method telea    # 快速修复（默认）
--inpaint-method ns       # 精细修复
```

### 完整示例
```bash
# 针对文字水印的最佳配置
python watermark_remover.py -i input_images -o output_images --mask-method text --inpaint-method telea
```

## 🎯 针对你的水印类型

你的图片包含 "www.abskoop.com" 类型的重复文字水印，建议使用：

```bash
source lama_env/bin/activate
python watermark_remover.py -i input_images -o output_images --mask-method text
```

## 📊 处理结果

处理完成后，你会得到：
- `removed_*.jpg` - 去除水印后的图片
- `*_mask.jpg` - 生成的遮罩图片（显示检测到的水印区域）

## 🔧 效果调优

如果效果不理想，可以：

1. **调整检测阈值**：编辑 `watermark_remover.py` 中的参数
   - `text_threshold=180` → 调整为 160-200
   - `threshold=200` → 调整为 180-220

2. **尝试不同算法**：
   ```bash
   --inpaint-method ns  # 更精细但较慢
   ```

3. **检查遮罩**：查看生成的 `*_mask.jpg` 文件，确认水印区域检测是否准确

## 🎉 测试结果

已成功处理测试图片：
- ✅ 输入：`input_images/test_with_watermark.jpg`
- ✅ 输出：`output_images/removed_test_with_watermark.jpg`
- ✅ 遮罩：`output_images/removed_test_with_watermark_mask.jpg`

## 💡 使用提示

1. **批量处理**：一次性处理整个目录的所有图片
2. **支持格式**：JPG, JPEG, PNG, BMP, TIFF
3. **自动命名**：输出文件自动添加 `removed_` 前缀
4. **遮罩调试**：检查生成的遮罩文件来优化参数
5. **备份原图**：工具不会修改原始文件

现在你可以开始处理你的图片了！🚀
