#!/usr/bin/env python3
"""
创建带有 www.nerdsbits.com 水印的测试图片
"""

import cv2
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import os

def create_nerdsbits_watermark_image():
    """创建带有 www.nerdsbits.com 水印的测试图片"""
    
    # 创建基础图片 (彩色渐变背景)
    width, height = 1200, 800
    
    # 创建彩色渐变背景
    image = np.zeros((height, width, 3), dtype=np.uint8)
    
    # 创建渐变效果
    for y in range(height):
        for x in range(width):
            # 彩虹渐变
            r = int(255 * (x / width))
            g = int(255 * (y / height))
            b = int(255 * ((x + y) / (width + height)))
            image[y, x] = [b, g, r]  # BGR格式
    
    # 添加一些几何形状作为内容
    # 添加圆形
    cv2.circle(image, (300, 200), 80, (100, 100, 100), -1)
    cv2.circle(image, (900, 600), 120, (150, 150, 150), -1)
    
    # 添加矩形
    cv2.rectangle(image, (500, 300), (700, 500), (80, 80, 80), -1)
    
    # 转换为PIL图像以添加文字
    pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
    draw = ImageDraw.Draw(pil_image)
    
    # 尝试使用系统字体
    try:
        # 在macOS上尝试使用系统字体
        font_size = 24
        font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", font_size)
    except:
        try:
            # 备选字体
            font = ImageFont.truetype("/System/Library/Fonts/Helvetica.ttc", font_size)
        except:
            # 使用默认字体
            font = ImageFont.load_default()
    
    # 添加 www.nerdsbits.com 水印
    watermark_text = "www.nerdsbits.com"
    
    # 计算水印位置 - 重复模式
    positions = []
    for y in range(0, height, 150):  # 每150像素重复一次
        for x in range(0, width, 200):  # 每200像素重复一次
            positions.append((x + 50, y + 50))
    
    # 添加水印文字
    for pos in positions:
        # 半透明白色水印
        draw.text(pos, watermark_text, fill=(200, 200, 200, 180), font=font)
    
    # 转换回OpenCV格式
    final_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
    
    return final_image

def create_angled_nerdsbits_watermark():
    """创建带有倾斜 www.nerdsbits.com 水印的图片"""
    
    # 创建基础图片
    width, height = 1200, 800
    image = np.zeros((height, width, 3), dtype=np.uint8)
    
    # 创建更复杂的背景
    for y in range(height):
        for x in range(width):
            # 波浪渐变
            r = int(128 + 127 * np.sin(x * 0.01))
            g = int(128 + 127 * np.sin(y * 0.01))
            b = int(128 + 127 * np.sin((x + y) * 0.005))
            image[y, x] = [b, g, r]
    
    # 添加一些内容
    cv2.ellipse(image, (400, 300), (150, 100), 45, 0, 360, (120, 120, 120), -1)
    cv2.ellipse(image, (800, 500), (100, 150), -30, 0, 360, (140, 140, 140), -1)
    
    # 转换为PIL添加倾斜水印
    pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
    
    # 创建水印层
    watermark_layer = Image.new('RGBA', (width, height), (0, 0, 0, 0))
    draw = ImageDraw.Draw(watermark_layer)
    
    try:
        font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 20)
    except:
        font = ImageFont.load_default()
    
    watermark_text = "www.nerdsbits.com"
    
    # 添加倾斜的重复水印
    for y in range(-200, height + 200, 120):
        for x in range(-300, width + 300, 180):
            # 创建临时图像用于旋转
            temp_img = Image.new('RGBA', (200, 50), (0, 0, 0, 0))
            temp_draw = ImageDraw.Draw(temp_img)
            temp_draw.text((10, 10), watermark_text, fill=(190, 190, 190, 150), font=font)
            
            # 旋转水印
            rotated = temp_img.rotate(25, expand=True)
            
            # 粘贴到主图像
            watermark_layer.paste(rotated, (x, y), rotated)
    
    # 合并图层
    final_image = Image.alpha_composite(pil_image.convert('RGBA'), watermark_layer)
    final_image = cv2.cvtColor(np.array(final_image.convert('RGB')), cv2.COLOR_RGB2BGR)
    
    return final_image

def main():
    # 确保测试目录存在
    os.makedirs('test_images', exist_ok=True)
    
    print("创建 www.nerdsbits.com 水印测试图片...")
    
    # 创建正常水印图片
    image1 = create_nerdsbits_watermark_image()
    cv2.imwrite('test_images/nerdsbits_watermark_test.jpg', image1)
    print("✅ 已创建: test_images/nerdsbits_watermark_test.jpg")
    
    # 创建倾斜水印图片
    image2 = create_angled_nerdsbits_watermark()
    cv2.imwrite('test_images/nerdsbits_angled_watermark.jpg', image2)
    print("✅ 已创建: test_images/nerdsbits_angled_watermark.jpg")
    
    print("\n🎯 测试图片创建完成！")
    print("现在可以使用以下命令测试水印去除效果：")
    print("python ultra_watermark_remover.py --input test_images/nerdsbits_watermark_test.jpg --output result_nerdsbits.jpg --debug")

if __name__ == "__main__":
    main()
