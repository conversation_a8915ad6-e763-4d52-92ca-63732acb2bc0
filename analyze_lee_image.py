#!/usr/bin/env python3
"""
专门分析 lee 图片的水印去除效果
"""

import cv2
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path

def analyze_watermark_removal_quality(original_path, result_path):
    """分析水印去除质量"""
    original = cv2.imread(str(original_path))
    result = cv2.imread(str(result_path))
    
    if original is None or result is None:
        return None
    
    # 确保尺寸一致
    if original.shape != result.shape:
        result = cv2.resize(result, (original.shape[1], original.shape[0]))
    
    # 计算差异
    diff = cv2.absdiff(original, result)
    diff_gray = cv2.cvtColor(diff, cv2.COLOR_BGR2GRAY)
    
    # 计算质量指标
    mse = np.mean(diff_gray ** 2)
    psnr = 20 * np.log10(255.0 / np.sqrt(mse)) if mse > 0 else float('inf')
    
    # 计算修改区域比例
    threshold = 10
    changed_pixels = np.sum(diff_gray > threshold)
    total_pixels = diff_gray.shape[0] * diff_gray.shape[1]
    change_ratio = changed_pixels / total_pixels
    
    return {
        'mse': mse,
        'psnr': psnr,
        'change_ratio': change_ratio
    }

def create_lee_comparison():
    """创建 lee 图片的对比分析"""
    
    original_path = "input_images/lee_f5d42015103a8772117281383f5c0948.jpg"
    
    results = {
        'Smart版本': {
            'path': 'output_images/lee_smart_removed.jpg',
            'mask': 'output_images/debug/lee_smart_removed_mask.png'
        },
        'Ultra版本': {
            'path': 'output_images/lee_ultra_removed.jpg', 
            'mask': 'output_images/debug/lee_ultra_removed_ultra_mask.png'
        }
    }
    
    # 读取原始图片
    original = cv2.imread(original_path)
    if original is None:
        print(f"❌ 无法读取原始图片: {original_path}")
        return
    
    original_rgb = cv2.cvtColor(original, cv2.COLOR_BGR2RGB)
    
    print("🔍 分析 Lee 图片的水印去除效果")
    print("=" * 50)
    print(f"📷 原始图片尺寸: {original.shape}")
    
    # 创建对比图
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    # 第一行：原图和处理结果
    axes[0, 0].imshow(original_rgb)
    axes[0, 0].set_title('原始图片 (带水印)', fontsize=14, fontweight='bold', color='red')
    axes[0, 0].axis('off')
    
    col = 1
    for method_name, result_info in results.items():
        result_path = result_info['path']
        
        if Path(result_path).exists():
            result = cv2.imread(result_path)
            result_rgb = cv2.cvtColor(result, cv2.COLOR_BGR2RGB)
            
            # 分析质量
            quality = analyze_watermark_removal_quality(original_path, result_path)
            
            axes[0, col].imshow(result_rgb)
            
            title = f'{method_name}\n'
            if quality:
                title += f'PSNR: {quality["psnr"]:.1f}dB\n'
                title += f'修改: {quality["change_ratio"]*100:.1f}%'
                
                print(f"\n📊 {method_name}:")
                print(f"   PSNR: {quality['psnr']:.2f} dB")
                print(f"   修改比例: {quality['change_ratio']:.2%}")
                
                # 质量评级
                if quality['psnr'] > 35:
                    grade = "🟢 优秀"
                    color = 'green'
                elif quality['psnr'] > 30:
                    grade = "🟡 良好"
                    color = 'orange'
                elif quality['psnr'] > 25:
                    grade = "🟠 可接受"
                    color = 'orange'
                else:
                    grade = "🔴 较差"
                    color = 'red'
                
                print(f"   质量评级: {grade}")
                
                # 添加彩色边框
                for spine in axes[0, col].spines.values():
                    spine.set_edgecolor(color)
                    spine.set_linewidth(3)
                    spine.set_visible(True)
            
            axes[0, col].set_title(title, fontsize=12)
            axes[0, col].axis('off')
            
            col += 1
        else:
            print(f"❌ {method_name}: 结果文件不存在")
    
    # 第二行：检测遮罩
    axes[1, 0].text(0.5, 0.5, '检测遮罩对比', ha='center', va='center', 
                   transform=axes[1, 0].transAxes, fontsize=16, fontweight='bold')
    axes[1, 0].axis('off')
    
    col = 1
    for method_name, result_info in results.items():
        mask_path = result_info['mask']
        
        if Path(mask_path).exists():
            mask = cv2.imread(mask_path, cv2.IMREAD_GRAYSCALE)
            
            axes[1, col].imshow(mask, cmap='gray')
            axes[1, col].set_title(f'{method_name} - 检测遮罩', fontsize=12)
            axes[1, col].axis('off')
            
            # 计算遮罩统计
            mask_area = np.sum(mask == 255)
            total_area = mask.shape[0] * mask.shape[1]
            mask_ratio = mask_area / total_area
            
            print(f"   检测区域占比: {mask_ratio:.2%}")
            
            col += 1
        else:
            print(f"❌ {method_name}: 遮罩文件不存在")
    
    plt.tight_layout()
    
    # 保存对比图
    output_path = "output_images/lee_comparison_analysis.png"
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f"\n✅ 对比分析图已保存: {output_path}")
    
    # 生成建议
    print(f"\n💡 处理建议:")
    print("   1. 如果Smart版本检测不足，使用Ultra版本")
    print("   2. 检查遮罩是否完全覆盖水印区域")
    print("   3. 可以手动调整检测参数以获得更好效果")
    
    return True

def main():
    print("🎯 开始分析 Lee 图片...")
    create_lee_comparison()

if __name__ == "__main__":
    main()
