#!/usr/bin/env python3
"""
简单的水印特征分析工具
"""

import numpy as np
from PIL import Image

def analyze_watermark_simple(image_path):
    """
    简单分析水印特征
    """
    print(f"分析图片: {image_path}")
    
    with Image.open(image_path) as img:
        if img.mode != 'RGB':
            img = img.convert('RGB')
        
        img_array = np.array(img)
        height, width = img_array.shape[:2]
        
        # 转换为灰度
        gray = np.dot(img_array[...,:3], [0.2989, 0.5870, 0.1140])
        
        print(f"图片尺寸: {width} x {height}")
        print(f"灰度值范围: {gray.min():.1f} - {gray.max():.1f}")
        print(f"平均灰度: {gray.mean():.1f}")
        
        # 分析灰度分布
        print("\n灰度分布分析:")
        ranges_to_test = [
            (0, 50, "很深"),
            (50, 100, "深色"),
            (100, 150, "中深色"), 
            (150, 200, "中等"),
            (200, 240, "浅色"),
            (240, 255, "很浅")
        ]
        
        for min_val, max_val, desc in ranges_to_test:
            mask = (gray >= min_val) & (gray <= max_val)
            percentage = np.sum(mask) / gray.size * 100
            print(f"  {desc} ({min_val}-{max_val}): {percentage:5.1f}%")
        
        # 寻找文字特征
        print("\n寻找文字特征:")
        
        # 计算梯度来检测边缘
        grad_x = np.gradient(gray, axis=1)
        grad_y = np.gradient(gray, axis=0)
        gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2)
        
        # 分析不同强度的边缘
        edge_thresholds = [5, 10, 15, 20, 25, 30]
        for threshold in edge_thresholds:
            edge_mask = gradient_magnitude > threshold
            edge_percentage = np.sum(edge_mask) / gray.size * 100
            print(f"  边缘强度 > {threshold}: {edge_percentage:5.2f}%")
        
        # 分析局部区域的特征
        print("\n局部区域分析:")
        block_size = 80
        step = 40
        
        watermark_regions = []
        
        for y in range(0, height - block_size, step):
            for x in range(0, width - block_size, step):
                block = gray[y:y+block_size, x:x+block_size]
                block_grad = gradient_magnitude[y:y+block_size, x:x+block_size]
                
                mean_gray = np.mean(block)
                std_gray = np.std(block)
                edge_density = np.sum(block_grad > 10) / block_grad.size
                
                # 检测可能的水印特征
                # 进一步放宽条件
                if (170 < mean_gray < 235 and  # 更宽的灰度范围
                    3 < std_gray < 30 and      # 更宽的对比度范围
                    edge_density > 0.02):      # 降低边缘密度要求
                    
                    watermark_regions.append({
                        'x': x, 'y': y,
                        'mean': mean_gray,
                        'std': std_gray,
                        'edge_density': edge_density
                    })
        
        print(f"检测到 {len(watermark_regions)} 个可能的水印区域")
        
        if watermark_regions:
            print("\n前10个最可能的水印区域:")
            # 按边缘密度排序
            watermark_regions.sort(key=lambda x: x['edge_density'], reverse=True)
            
            for i, region in enumerate(watermark_regions[:10]):
                print(f"  区域 {i+1}: 位置({region['x']}, {region['y']}) "
                      f"灰度={region['mean']:.1f} "
                      f"对比度={region['std']:.1f} "
                      f"边缘密度={region['edge_density']:.3f}")
        
        return watermark_regions

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) != 2:
        print("用法: python simple_analyze.py <图片路径>")
        sys.exit(1)
    
    analyze_watermark_simple(sys.argv[1])
