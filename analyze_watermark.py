#!/usr/bin/env python3
"""
分析水印特征工具
用于分析图片中水印的具体特征
"""

import numpy as np
from PIL import Image
import sys

def analyze_watermark_features(image_path):
    """
    详细分析水印特征
    """
    print(f"分析图片: {image_path}")
    
    with Image.open(image_path) as img:
        if img.mode != 'RGB':
            img = img.convert('RGB')
        
        img_array = np.array(img)
        height, width = img_array.shape[:2]
        
        # 转换为灰度
        gray = np.dot(img_array[...,:3], [0.2989, 0.5870, 0.1140])
        
        print(f"图片尺寸: {width} x {height}")
        print(f"灰度值范围: {gray.min():.1f} - {gray.max():.1f}")
        print(f"平均灰度: {gray.mean():.1f}")
        
        # 分析灰度分布
        hist, bins = np.histogram(gray.flatten(), bins=50, range=(0, 255))
        
        print("\n灰度分布分析:")
        for i in range(0, len(bins)-1, 5):
            start = bins[i]
            end = bins[i+5] if i+5 < len(bins) else bins[-1]
            count = np.sum(hist[i:i+5])
            percentage = count / gray.size * 100
            print(f"  {start:3.0f}-{end:3.0f}: {percentage:5.1f}%")
        
        # 寻找可能的水印区域
        print("\n水印区域分析:")
        
        # 测试不同的亮度范围
        ranges_to_test = [
            (50, 100, "深灰色"),
            (100, 150, "中深灰色"), 
            (150, 200, "中等灰色"),
            (200, 240, "浅灰色")
        ]
        
        for min_val, max_val, desc in ranges_to_test:
            mask = (gray >= min_val) & (gray <= max_val)
            percentage = np.sum(mask) / gray.size * 100
            print(f"  {desc} ({min_val}-{max_val}): {percentage:5.1f}%")
        
        # 分析局部区域
        print("\n局部区域分析 (100x100 块):")
        block_size = 100
        watermark_candidates = []
        
        for y in range(0, height - block_size, block_size):
            for x in range(0, width - block_size, block_size):
                block = gray[y:y+block_size, x:x+block_size]
                
                mean_val = np.mean(block)
                std_val = np.std(block)
                min_val = np.min(block)
                max_val = np.max(block)
                
                # 检查是否可能包含水印
                # 水印区域通常有一定的对比度变化
                if std_val > 10 and std_val < 50:
                    watermark_candidates.append({
                        'position': (x, y),
                        'mean': mean_val,
                        'std': std_val,
                        'min': min_val,
                        'max': max_val
                    })
        
        print(f"找到 {len(watermark_candidates)} 个可能的水印区域")
        
        if watermark_candidates:
            # 按标准差排序，显示最有可能的区域
            watermark_candidates.sort(key=lambda x: x['std'])
            
            print("\n最可能的水印区域 (按对比度排序):")
            for i, candidate in enumerate(watermark_candidates[:10]):
                pos = candidate['position']
                print(f"  区域 {i+1}: 位置({pos[0]}, {pos[1]}) "
                      f"平均值={candidate['mean']:.1f} "
                      f"标准差={candidate['std']:.1f} "
                      f"范围={candidate['min']:.1f}-{candidate['max']:.1f}")
        
        # 保存分析结果图
        save_analysis_image(gray, watermark_candidates, "watermark_analysis.png")

def save_analysis_image(gray, candidates, output_path):
    """
    保存分析结果图
    """
    try:
        import matplotlib.pyplot as plt
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 显示原图
        ax1.imshow(gray, cmap='gray')
        ax1.set_title('原图 (灰度)')
        ax1.axis('off')
        
        # 标记候选区域
        ax2.imshow(gray, cmap='gray')
        for i, candidate in enumerate(candidates[:20]):  # 只显示前20个
            x, y = candidate['position']
            rect = plt.Rectangle((x, y), 100, 100, 
                               fill=False, edgecolor='red', linewidth=1)
            ax2.add_patch(rect)
            ax2.text(x, y-5, str(i+1), color='red', fontsize=8)
        
        ax2.set_title(f'检测到的候选区域 (前20个)')
        ax2.axis('off')
        
        plt.tight_layout()
        plt.savefig(output_path, dpi=150, bbox_inches='tight')
        print(f"\n分析结果已保存到: {output_path}")
        
    except ImportError:
        print("matplotlib 未安装，跳过可视化")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) != 2:
        print("用法: python analyze_watermark.py <图片路径>")
        sys.exit(1)
    
    analyze_watermark_features(sys.argv[1])
