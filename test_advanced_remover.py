#!/usr/bin/env python3
"""
测试高级水印去除工具
创建模拟 www.abskoop.com 类型的重复、半透明、对角线排布水印
"""

import cv2
import numpy as np
from pathlib import Path
import math

def create_realistic_watermark_image():
    """
    创建一个真实的带有 www.abskoop.com 类型水印的图片
    特征：重复、半透明、对角线排布、覆盖范围大
    """
    # 创建一个更大的图片
    height, width = 800, 1200
    
    # 创建真实感的背景图片
    image = create_realistic_background(height, width)
    
    # 添加 www.abskoop.com 类型的水印
    watermarked_image = add_abskoop_watermark(image)
    
    return watermarked_image

def create_realistic_background(height, width):
    """创建真实感的背景图片"""
    # 创建渐变背景
    image = np.zeros((height, width, 3), dtype=np.uint8)
    
    # 多层渐变效果
    for i in range(height):
        for j in range(width):
            # 复杂的颜色渐变
            r = int(200 + 55 * math.sin(i * 0.01) * math.cos(j * 0.008))
            g = int(180 + 75 * math.cos(i * 0.008) * math.sin(j * 0.01))
            b = int(160 + 95 * math.sin(i * 0.006) * math.sin(j * 0.012))
            
            image[i, j] = [max(0, min(255, b)), max(0, min(255, g)), max(0, min(255, r))]
    
    # 添加一些内容区域
    add_content_areas(image)
    
    return image

def add_content_areas(image):
    """添加一些模拟内容区域"""
    height, width = image.shape[:2]
    
    # 添加几个矩形内容区域
    content_areas = [
        (100, 150, 300, 200),   # x, y, w, h
        (500, 100, 400, 150),
        (200, 400, 350, 180),
        (700, 300, 300, 250),
        (150, 600, 500, 120)
    ]
    
    for x, y, w, h in content_areas:
        if x + w < width and y + h < height:
            # 创建内容区域
            cv2.rectangle(image, (x, y), (x + w, y + h), (240, 240, 240), -1)
            cv2.rectangle(image, (x, y), (x + w, y + h), (100, 100, 100), 2)
            
            # 添加一些文字
            cv2.putText(image, "Content Area", (x + 10, y + 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (50, 50, 50), 2)
            cv2.putText(image, "Important Information", (x + 10, y + 60), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (80, 80, 80), 1)

def add_abskoop_watermark(image):
    """
    添加 www.abskoop.com 类型的水印
    特征：重复、半透明、对角线排布、覆盖范围大
    """
    height, width = image.shape[:2]
    watermark_text = "www.abskoop.com"
    
    # 创建水印层
    watermark_layer = np.zeros((height, width, 3), dtype=np.uint8)
    
    # 字体设置
    font = cv2.FONT_HERSHEY_SIMPLEX
    font_scale = 0.8
    thickness = 2
    
    # 计算文字尺寸
    (text_width, text_height), baseline = cv2.getTextSize(watermark_text, font, font_scale, thickness)
    
    # 对角线排布参数
    diagonal_spacing_x = 200  # 水平间距
    diagonal_spacing_y = 150  # 垂直间距
    angle = 30  # 对角线角度
    
    # 计算旋转后的间距
    angle_rad = math.radians(angle)
    cos_angle = math.cos(angle_rad)
    sin_angle = math.sin(angle_rad)
    
    # 生成对角线网格位置
    positions = []
    for y in range(-200, height + 200, diagonal_spacing_y):
        for x in range(-200, width + 200, diagonal_spacing_x):
            # 对角线偏移
            offset_x = int(y * 0.5)  # 对角线效果
            final_x = x + offset_x
            final_y = y
            
            if -100 <= final_x <= width + 100 and -100 <= final_y <= height + 100:
                positions.append((final_x, final_y))
    
    # 在每个位置添加水印
    for pos_x, pos_y in positions:
        # 添加一些随机偏移，使水印看起来更自然
        random_offset_x = np.random.randint(-20, 21)
        random_offset_y = np.random.randint(-15, 16)
        
        final_x = pos_x + random_offset_x
        final_y = pos_y + random_offset_y
        
        # 确保位置在图片范围内
        if 0 <= final_x <= width - text_width and 0 <= final_y <= height:
            # 水印颜色（浅灰色，半透明效果）
            color = (200, 200, 200)  # 浅灰色
            
            # 添加水印文字
            cv2.putText(watermark_layer, watermark_text, (final_x, final_y), 
                       font, font_scale, color, thickness)
    
    # 创建半透明效果
    # 方法1：直接混合
    alpha = 0.3  # 透明度
    watermarked = cv2.addWeighted(image, 1.0, watermark_layer, alpha, 0)
    
    # 方法2：添加更真实的半透明效果
    # 创建一个稍微不同的混合
    for i in range(height):
        for j in range(width):
            if np.any(watermark_layer[i, j] > 0):  # 如果有水印
                # 更复杂的混合算法，模拟真实的半透明水印
                original = image[i, j].astype(np.float32)
                watermark = watermark_layer[i, j].astype(np.float32)
                
                # 模拟半透明效果
                mixed = original * 0.75 + watermark * 0.25
                
                # 添加一些亮度变化
                brightness_factor = 1.1
                mixed = mixed * brightness_factor
                
                # 确保值在有效范围内
                mixed = np.clip(mixed, 0, 255)
                watermarked[i, j] = mixed.astype(np.uint8)
    
    return watermarked

def create_test_images():
    """创建多个测试图片"""
    output_dir = Path("test_images")
    output_dir.mkdir(exist_ok=True)
    
    print("🎨 创建测试图片...")
    
    # 创建主要测试图片
    main_image = create_realistic_watermark_image()
    main_path = output_dir / "abskoop_watermark_test.jpg"
    cv2.imwrite(str(main_path), main_image)
    print(f"✅ 主测试图片已保存: {main_path}")
    
    # 创建不同强度的水印图片
    create_variant_images(output_dir)
    
    print(f"🎉 测试图片创建完成，保存在: {output_dir}")
    return output_dir

def create_variant_images(output_dir):
    """创建不同变体的测试图片"""
    base_image = create_realistic_background(600, 900)
    
    # 变体1：更密集的水印
    dense_image = add_dense_watermark(base_image.copy())
    cv2.imwrite(str(output_dir / "dense_watermark.jpg"), dense_image)
    
    # 变体2：更淡的水印
    light_image = add_light_watermark(base_image.copy())
    cv2.imwrite(str(output_dir / "light_watermark.jpg"), light_image)
    
    # 变体3：不同角度的水印
    angled_image = add_angled_watermark(base_image.copy())
    cv2.imwrite(str(output_dir / "angled_watermark.jpg"), angled_image)

def add_dense_watermark(image):
    """添加密集的水印"""
    height, width = image.shape[:2]
    watermark_text = "www.abskoop.com"
    font = cv2.FONT_HERSHEY_SIMPLEX
    
    # 更小的间距
    for y in range(0, height, 80):
        for x in range(0, width, 120):
            offset_x = int(y * 0.3)
            final_x = x + offset_x
            if final_x < width - 150:
                cv2.putText(image, watermark_text, (final_x, y), 
                           font, 0.6, (210, 210, 210), 1)
    
    return image

def add_light_watermark(image):
    """添加很淡的水印"""
    height, width = image.shape[:2]
    watermark_text = "www.abskoop.com"
    font = cv2.FONT_HERSHEY_SIMPLEX
    
    # 创建水印层
    watermark_layer = np.zeros_like(image)
    
    for y in range(0, height, 120):
        for x in range(0, width, 180):
            offset_x = int(y * 0.4)
            final_x = x + offset_x
            if final_x < width - 150:
                cv2.putText(watermark_layer, watermark_text, (final_x, y), 
                           font, 0.7, (230, 230, 230), 1)
    
    # 非常淡的混合
    result = cv2.addWeighted(image, 0.9, watermark_layer, 0.1, 0)
    return result

def add_angled_watermark(image):
    """添加不同角度的水印"""
    height, width = image.shape[:2]
    watermark_text = "www.abskoop.com"
    font = cv2.FONT_HERSHEY_SIMPLEX
    
    # 45度角排布
    for y in range(0, height, 100):
        for x in range(0, width, 150):
            offset_x = int(y * 0.8)  # 更陡的角度
            final_x = x + offset_x
            if final_x < width - 150:
                cv2.putText(image, watermark_text, (final_x, y), 
                           font, 0.7, (195, 195, 195), 2)
    
    return image

def test_watermark_removal():
    """测试水印去除效果"""
    print("🧪 开始测试高级水印去除工具...")
    
    # 创建测试图片
    test_dir = create_test_images()
    
    # 创建输出目录
    output_dir = Path("test_results_advanced")
    output_dir.mkdir(exist_ok=True)
    
    print("\n🔧 测试不同配置的水印去除效果...")
    
    # 测试命令
    test_commands = [
        # 基本测试
        f"python advanced_watermark_remover.py -i {test_dir} -o {output_dir}/basic --debug",
        
        # 激进模式测试
        f"python advanced_watermark_remover.py -i {test_dir} -o {output_dir}/aggressive --debug",
        
        # 保守模式测试
        f"python advanced_watermark_remover.py -i {test_dir} -o {output_dir}/conservative --debug",
    ]
    
    print("📋 建议的测试命令:")
    for i, cmd in enumerate(test_commands, 1):
        print(f"{i}. {cmd}")
    
    print(f"\n📁 测试图片位置: {test_dir}")
    print(f"📁 结果保存位置: {output_dir}")
    
    return test_dir, output_dir

if __name__ == "__main__":
    test_watermark_removal()
