#!/usr/bin/env python3
"""
激进版水印去除工具
专门针对密集分布的 WWW.ABSKOOP.COM 水印
"""

import os
import sys
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

try:
    from PIL import Image, ImageFilter, ImageEnhance
    import numpy as np
except ImportError:
    logger.error("需要安装 Pillow 和 numpy: pip install Pillow numpy")
    sys.exit(1)

class AggressiveWatermarkRemover:
    def __init__(self):
        self.processed_count = 0
        
    def detect_all_watermark_regions(self, image_path):
        """
        激进检测所有可能的水印区域
        """
        try:
            with Image.open(image_path) as img:
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                img_array = np.array(img)
                height, width = img_array.shape[:2]
                
                # 转换为灰度
                gray = np.dot(img_array[...,:3], [0.2989, 0.5870, 0.1140])
                
                regions = []
                
                # 使用非常小的块进行密集扫描
                block_size = 40
                step = 20  # 50% 重叠
                
                logger.info(f"开始密集扫描，块大小={block_size}, 步长={step}")
                
                for y in range(0, height - block_size, step):
                    for x in range(0, width - block_size, step):
                        block = gray[y:y+block_size, x:x+block_size]
                        
                        mean_brightness = np.mean(block)
                        std_brightness = np.std(block)
                        
                        # 非常宽松的检测条件
                        # 只要不是纯白色背景就认为可能有水印
                        if (mean_brightness < 245 and  # 不是纯白
                            std_brightness > 2):       # 有一点变化
                            
                            regions.append((x, y, x + block_size, y + block_size))
                
                logger.info(f"初步检测到 {len(regions)} 个区域")
                
                # 简单去重 - 只保留不完全重叠的区域
                filtered_regions = self._filter_regions(regions)
                
                logger.info(f"过滤后剩余 {len(filtered_regions)} 个区域")
                return filtered_regions
                
        except Exception as e:
            logger.error(f"检测失败 {image_path}: {e}")
            return []
    
    def _filter_regions(self, regions):
        """
        过滤重叠区域
        """
        if not regions:
            return []
        
        # 按位置排序
        regions.sort(key=lambda r: (r[1], r[0]))
        
        filtered = []
        for region in regions:
            x1, y1, x2, y2 = region
            
            # 检查是否与已有区域重叠超过50%
            overlap = False
            for existing in filtered:
                ex1, ey1, ex2, ey2 = existing
                
                # 计算重叠面积
                overlap_x1 = max(x1, ex1)
                overlap_y1 = max(y1, ey1)
                overlap_x2 = min(x2, ex2)
                overlap_y2 = min(y2, ey2)
                
                if overlap_x1 < overlap_x2 and overlap_y1 < overlap_y2:
                    overlap_area = (overlap_x2 - overlap_x1) * (overlap_y2 - overlap_y1)
                    region_area = (x2 - x1) * (y2 - y1)
                    
                    if overlap_area / region_area > 0.5:  # 重叠超过50%
                        overlap = True
                        break
            
            if not overlap:
                filtered.append(region)
        
        return filtered
    
    def remove_watermark_aggressive(self, image_path, output_path, regions):
        """
        激进去除水印
        """
        try:
            with Image.open(image_path) as img:
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                logger.info(f"开始处理 {len(regions)} 个区域")
                
                # 对每个区域进行处理
                for i, region in enumerate(regions):
                    x1, y1, x2, y2 = region
                    
                    # 提取区域
                    roi = img.crop((x1, y1, x2, y2))
                    
                    # 多重处理
                    # 1. 强高斯模糊
                    blurred_roi = roi.filter(ImageFilter.GaussianBlur(radius=4))
                    
                    # 2. 大幅降低对比度
                    enhancer = ImageEnhance.Contrast(blurred_roi)
                    low_contrast = enhancer.enhance(0.3)  # 大幅降低对比度
                    
                    # 3. 提高亮度使其接近背景
                    brightness_enhancer = ImageEnhance.Brightness(low_contrast)
                    brightened = brightness_enhancer.enhance(1.3)  # 提亮
                    
                    # 4. 再次模糊
                    final_roi = brightened.filter(ImageFilter.GaussianBlur(radius=2))
                    
                    # 将处理后的区域粘贴回去
                    img.paste(final_roi, (x1, y1))
                    
                    if (i + 1) % 100 == 0:
                        logger.info(f"已处理 {i + 1}/{len(regions)} 个区域")
                
                # 最后对整个图片进行轻微处理
                # 轻微模糊整个图片来进一步减少水印痕迹
                img = img.filter(ImageFilter.GaussianBlur(radius=0.5))
                
                # 保存结果
                img.save(output_path, quality=95)
                return True
                
        except Exception as e:
            logger.error(f"修复失败 {image_path}: {e}")
            return False
    
    def process_single_image(self, input_path, output_path):
        """
        处理单张图片
        """
        try:
            logger.info(f"处理: {input_path}")
            
            # 检测水印区域
            regions = self.detect_all_watermark_regions(input_path)
            
            if not regions:
                logger.info(f"未检测到水印，复制原图: {input_path}")
                import shutil
                shutil.copy2(input_path, output_path)
                return True
            
            # 去除水印
            success = self.remove_watermark_aggressive(input_path, output_path, regions)
            
            if success:
                logger.info(f"处理完成: {output_path}")
            
            return success
            
        except Exception as e:
            logger.error(f"处理失败 {input_path}: {e}")
            return False

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='激进版水印去除工具')
    parser.add_argument('--input', '-i', required=True, help='输入目录或文件')
    parser.add_argument('--output', '-o', required=True, help='输出目录或文件')
    
    args = parser.parse_args()
    
    remover = AggressiveWatermarkRemover()
    
    logger.info("🚀 激进版水印去除工具启动")
    logger.info("=" * 50)
    
    input_path = Path(args.input)
    output_path = Path(args.output)
    
    if input_path.is_file():
        # 处理单个文件
        output_path.parent.mkdir(parents=True, exist_ok=True)
        remover.process_single_image(input_path, output_path)
    else:
        # 批量处理
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 收集图片文件
        supported_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
        image_files = []
        for ext in supported_formats:
            image_files.extend(input_path.glob(f"*{ext}"))
            image_files.extend(input_path.glob(f"*{ext.upper()}"))
        
        if not image_files:
            logger.error(f"在 {args.input} 中未找到支持的图片文件")
            return
        
        logger.info(f"找到 {len(image_files)} 个图片文件")
        
        for img_file in image_files:
            output_file = output_path / img_file.name
            remover.process_single_image(img_file, output_file)

if __name__ == "__main__":
    main()
