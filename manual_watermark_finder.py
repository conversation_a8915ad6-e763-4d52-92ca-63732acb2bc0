#!/usr/bin/env python3
"""
手动水印查找工具
将图片分割成小块，让用户手动识别水印位置
"""

import numpy as np
from PIL import Image, ImageDraw, ImageFont
import os
import sys

def create_grid_analysis(image_path, grid_size=200):
    """
    将图片分割成网格，每个网格标号
    """
    print(f"分析图片: {image_path}")
    
    with Image.open(image_path) as img:
        if img.mode != 'RGB':
            img = img.convert('RGB')
        
        width, height = img.size
        print(f"图片尺寸: {width} x {height}")
        
        # 创建网格覆盖图
        overlay = img.copy()
        draw = ImageDraw.Draw(overlay)
        
        # 尝试加载字体
        try:
            font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 20)
        except:
            font = ImageFont.load_default()
        
        grid_info = []
        grid_id = 1
        
        # 绘制网格和编号
        for y in range(0, height, grid_size):
            for x in range(0, width, grid_size):
                # 网格边界
                x2 = min(x + grid_size, width)
                y2 = min(y + grid_size, height)
                
                # 绘制网格线
                draw.rectangle([x, y, x2, y2], outline='red', width=2)
                
                # 添加编号
                text_x = x + 10
                text_y = y + 10
                draw.text((text_x, text_y), str(grid_id), fill='red', font=font)
                
                # 分析这个网格的内容
                grid_region = img.crop((x, y, x2, y2))
                grid_array = np.array(grid_region)
                gray_grid = np.dot(grid_array[...,:3], [0.2989, 0.5870, 0.1140])
                
                mean_val = np.mean(gray_grid)
                std_val = np.std(gray_grid)
                
                grid_info.append({
                    'id': grid_id,
                    'position': (x, y),
                    'size': (x2-x, y2-y),
                    'mean': mean_val,
                    'std': std_val
                })
                
                grid_id += 1
        
        # 保存网格图
        grid_output = "grid_analysis.jpg"
        overlay.save(grid_output, quality=95)
        print(f"网格分析图已保存: {grid_output}")
        
        # 显示网格统计
        print(f"\n总共 {len(grid_info)} 个网格区域")
        print("\n网格统计 (按平均亮度排序):")
        print("ID\t位置\t\t尺寸\t\t平均亮度\t标准差")
        print("-" * 60)
        
        # 按亮度排序
        grid_info.sort(key=lambda x: x['mean'])
        
        for grid in grid_info:
            pos = grid['position']
            size = grid['size']
            print(f"{grid['id']}\t({pos[0]}, {pos[1]})\t\t{size[0]}x{size[1]}\t\t{grid['mean']:.1f}\t\t{grid['std']:.1f}")
        
        return grid_info

def extract_specific_grids(image_path, grid_ids, grid_size=200):
    """
    提取指定编号的网格区域
    """
    print(f"\n提取指定网格区域...")
    
    with Image.open(image_path) as img:
        if img.mode != 'RGB':
            img = img.convert('RGB')
        
        width, height = img.size
        
        grid_id = 1
        extracted_count = 0
        
        for y in range(0, height, grid_size):
            for x in range(0, width, grid_size):
                if grid_id in grid_ids:
                    x2 = min(x + grid_size, width)
                    y2 = min(y + grid_size, height)
                    
                    # 提取区域
                    region = img.crop((x, y, x2, y2))
                    
                    # 保存
                    output_name = f"grid_{grid_id}_{x}_{y}.jpg"
                    region.save(output_name, quality=95)
                    
                    print(f"已提取网格 {grid_id}: {output_name}")
                    extracted_count += 1
                
                grid_id += 1
        
        print(f"总共提取了 {extracted_count} 个网格区域")

def analyze_suspicious_grids(grid_info, threshold_std=10):
    """
    分析可疑的网格（可能包含水印）
    """
    print(f"\n分析可疑网格 (标准差 > {threshold_std}):")
    
    suspicious = [grid for grid in grid_info if grid['std'] > threshold_std]
    suspicious.sort(key=lambda x: x['std'], reverse=True)
    
    print(f"找到 {len(suspicious)} 个可疑网格:")
    print("ID\t位置\t\t平均亮度\t标准差\t可能性")
    print("-" * 50)
    
    for grid in suspicious[:20]:  # 只显示前20个
        pos = grid['position']
        possibility = "高" if grid['std'] > 15 else "中"
        print(f"{grid['id']}\t({pos[0]}, {pos[1]})\t\t{grid['mean']:.1f}\t\t{grid['std']:.1f}\t{possibility}")
    
    return [grid['id'] for grid in suspicious[:10]]  # 返回前10个最可疑的ID

def main():
    if len(sys.argv) < 2:
        print("用法:")
        print("  python manual_watermark_finder.py <图片路径>                    # 创建网格分析")
        print("  python manual_watermark_finder.py <图片路径> <网格ID1,ID2,...>  # 提取指定网格")
        sys.exit(1)
    
    image_path = sys.argv[1]
    
    if not os.path.exists(image_path):
        print(f"文件不存在: {image_path}")
        sys.exit(1)
    
    print("=" * 60)
    print("手动水印查找工具")
    print("=" * 60)
    
    if len(sys.argv) == 2:
        # 创建网格分析
        grid_info = create_grid_analysis(image_path)
        
        # 自动分析可疑区域
        suspicious_ids = analyze_suspicious_grids(grid_info)
        
        print(f"\n建议检查的网格ID: {suspicious_ids}")
        print(f"\n请查看 grid_analysis.jpg 文件，找到水印所在的网格")
        print(f"然后运行: python {sys.argv[0]} {image_path} <网格ID1,ID2,...>")
        
    else:
        # 提取指定网格
        grid_ids_str = sys.argv[2]
        try:
            grid_ids = [int(x.strip()) for x in grid_ids_str.split(',')]
            extract_specific_grids(image_path, grid_ids)
        except ValueError:
            print("网格ID格式错误，请使用逗号分隔的数字，如: 1,5,10")
            sys.exit(1)

if __name__ == "__main__":
    main()
