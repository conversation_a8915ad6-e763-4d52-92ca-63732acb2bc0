#!/usr/bin/env python3
"""
交互式水印检测工具
帮助用户可视化和调整检测参数
"""

import cv2
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import argparse

class InteractiveWatermarkDetector:
    def __init__(self):
        pass
    
    def analyze_image_content(self, image_path):
        """分析图像内容，帮助理解水印分布"""
        image = cv2.imread(str(image_path))
        if image is None:
            print(f"❌ 无法读取图片: {image_path}")
            return
        
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        height, width = gray.shape
        
        print(f"📷 图片分析: {image_path}")
        print(f"   尺寸: {width} x {height}")
        print(f"   平均亮度: {np.mean(gray):.1f}")
        print(f"   亮度标准差: {np.std(gray):.1f}")
        
        # 分析亮度分布
        hist = cv2.calcHist([gray], [0], None, [256], [0, 256])
        
        # 创建多种检测方法的可视化
        fig, axes = plt.subplots(3, 3, figsize=(18, 15))
        
        # 原图
        axes[0, 0].imshow(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
        axes[0, 0].set_title('原始图片', fontsize=12)
        axes[0, 0].axis('off')
        
        # 灰度图
        axes[0, 1].imshow(gray, cmap='gray')
        axes[0, 1].set_title('灰度图', fontsize=12)
        axes[0, 1].axis('off')
        
        # 亮度直方图
        axes[0, 2].plot(hist)
        axes[0, 2].set_title('亮度分布', fontsize=12)
        axes[0, 2].set_xlabel('亮度值')
        axes[0, 2].set_ylabel('像素数量')
        
        # 不同亮度范围的检测
        brightness_ranges = [
            (180, 220, "水印范围1"),
            (190, 230, "水印范围2"), 
            (170, 210, "水印范围3"),
            (160, 200, "较暗范围"),
            (200, 240, "较亮范围"),
            (150, 250, "宽范围")
        ]
        
        for i, (low, high, name) in enumerate(brightness_ranges):
            row = (i + 3) // 3
            col = (i + 3) % 3
            
            if row < 3 and col < 3:
                mask = cv2.inRange(gray, low, high)
                axes[row, col].imshow(mask, cmap='gray')
                axes[row, col].set_title(f'{name}\n({low}-{high})', fontsize=10)
                axes[row, col].axis('off')
                
                # 计算检测比例
                detection_ratio = np.sum(mask == 255) / (height * width)
                print(f"   {name} ({low}-{high}): {detection_ratio:.2%}")
        
        plt.tight_layout()
        
        # 保存分析图
        output_path = f"analysis_{Path(image_path).stem}.png"
        plt.savefig(output_path, dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"✅ 分析图已保存: {output_path}")
        
        # 边缘检测分析
        self._analyze_edges(gray, Path(image_path).stem)
        
        # 文字区域分析
        self._analyze_text_regions(gray, Path(image_path).stem)
    
    def _analyze_edges(self, gray, filename):
        """分析边缘特征"""
        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        
        # 不同参数的Canny边缘检测
        canny_params = [
            (30, 80, "温和"),
            (50, 150, "标准"),
            (20, 60, "敏感"),
            (40, 120, "保守")
        ]
        
        for i, (low, high, name) in enumerate(canny_params):
            if i < 6:
                row = i // 3
                col = i % 3
                
                edges = cv2.Canny(gray, low, high)
                axes[row, col].imshow(edges, cmap='gray')
                axes[row, col].set_title(f'Canny {name}\n({low}, {high})', fontsize=10)
                axes[row, col].axis('off')
                
                edge_density = np.sum(edges == 255) / (edges.shape[0] * edges.shape[1])
                print(f"   边缘密度 {name}: {edge_density:.3f}")
        
        # Sobel边缘检测
        sobelx = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
        sobely = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
        sobel_combined = np.sqrt(sobelx**2 + sobely**2)
        sobel_normalized = cv2.normalize(sobel_combined, None, 0, 255, cv2.NORM_MINMAX).astype(np.uint8)
        
        axes[1, 2].imshow(sobel_normalized, cmap='gray')
        axes[1, 2].set_title('Sobel边缘', fontsize=10)
        axes[1, 2].axis('off')
        
        plt.tight_layout()
        plt.savefig(f"edges_analysis_{filename}.png", dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"✅ 边缘分析图已保存: edges_analysis_{filename}.png")
    
    def _analyze_text_regions(self, gray, filename):
        """分析文字区域"""
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        
        # 自适应阈值
        adaptive_thresh = cv2.adaptiveThreshold(
            gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
        )
        axes[0, 0].imshow(adaptive_thresh, cmap='gray')
        axes[0, 0].set_title('自适应阈值', fontsize=12)
        axes[0, 0].axis('off')
        
        # 全局阈值
        _, global_thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        axes[0, 1].imshow(global_thresh, cmap='gray')
        axes[0, 1].set_title('全局阈值 (Otsu)', fontsize=12)
        axes[0, 1].axis('off')
        
        # 形态学操作
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
        morph_close = cv2.morphologyEx(adaptive_thresh, cv2.MORPH_CLOSE, kernel, iterations=2)
        axes[1, 0].imshow(morph_close, cmap='gray')
        axes[1, 0].set_title('形态学闭运算', fontsize=12)
        axes[1, 0].axis('off')
        
        # 轮廓检测和过滤
        contours, _ = cv2.findContours(adaptive_thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # 创建轮廓可视化
        contour_image = np.zeros_like(gray)
        text_contours = []
        
        for contour in contours:
            area = cv2.contourArea(contour)
            x, y, w, h = cv2.boundingRect(contour)
            aspect_ratio = w / h if h > 0 else 0
            
            # 过滤出可能的文字轮廓
            if 50 < area < 5000 and 0.2 < aspect_ratio < 10:
                text_contours.append(contour)
                cv2.drawContours(contour_image, [contour], -1, 255, -1)
        
        axes[1, 1].imshow(contour_image, cmap='gray')
        axes[1, 1].set_title(f'文字轮廓 ({len(text_contours)}个)', fontsize=12)
        axes[1, 1].axis('off')
        
        print(f"   检测到 {len(contours)} 个轮廓")
        print(f"   过滤后 {len(text_contours)} 个文字轮廓")
        
        plt.tight_layout()
        plt.savefig(f"text_analysis_{filename}.png", dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"✅ 文字分析图已保存: text_analysis_{filename}.png")
    
    def suggest_parameters(self, image_path):
        """根据图像分析建议参数"""
        image = cv2.imread(str(image_path))
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        mean_brightness = np.mean(gray)
        std_brightness = np.std(gray)
        
        print(f"\n💡 参数建议:")
        
        if mean_brightness > 200:
            print("   - 图像较亮，建议亮度范围: 190-230")
            brightness_range = (190, 230)
        elif mean_brightness > 150:
            print("   - 图像中等亮度，建议亮度范围: 180-220")
            brightness_range = (180, 220)
        else:
            print("   - 图像较暗，建议亮度范围: 160-200")
            brightness_range = (160, 200)
        
        if std_brightness > 50:
            print("   - 对比度较高，使用保守的边缘检测: (40, 120)")
            edge_params = (40, 120)
        else:
            print("   - 对比度较低，使用敏感的边缘检测: (20, 60)")
            edge_params = (20, 60)
        
        return {
            'brightness_range': brightness_range,
            'edge_params': edge_params
        }

def main():
    parser = argparse.ArgumentParser(description='交互式水印检测分析工具')
    parser.add_argument('--input', '-i', required=True, help='输入图片文件')
    
    args = parser.parse_args()
    
    detector = InteractiveWatermarkDetector()
    
    print("🔍 交互式水印检测分析工具")
    print("=" * 50)
    
    # 分析图像
    detector.analyze_image_content(args.input)
    
    # 建议参数
    suggestions = detector.suggest_parameters(args.input)
    
    print(f"\n🎯 建议的检测参数:")
    print(f"   亮度范围: {suggestions['brightness_range']}")
    print(f"   边缘参数: {suggestions['edge_params']}")
    
    print(f"\n📋 下一步:")
    print("1. 查看生成的分析图片")
    print("2. 根据分析结果调整检测参数")
    print("3. 重新运行精确检测工具")

if __name__ == "__main__":
    main()
