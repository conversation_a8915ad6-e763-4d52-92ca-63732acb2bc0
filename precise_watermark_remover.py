#!/usr/bin/env python3
"""
精确水印去除工具
只检测和去除 WWW.ABSKOOP.COM 水印文字
保护所有正常内容
"""

import os
import sys
from pathlib import Path
import logging
import re

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

try:
    from PIL import Image, ImageFilter, ImageEnhance, ImageDraw
    import numpy as np
except ImportError:
    logger.error("需要安装 Pillow 和 numpy: pip install Pillow numpy")
    sys.exit(1)

class PreciseWatermarkRemover:
    def __init__(self):
        # 只检测这些特定的水印文字
        self.watermark_patterns = [
            "WWW.ABSKOOP.COM",
            "ABSKOOP.COM", 
            "WWW.ABSKOOP",
            "ABSKOOP"
        ]
        
    def detect_watermark_text_only(self, image_path):
        """
        只检测特定的水印文字，忽略所有其他内容
        """
        try:
            with Image.open(image_path) as img:
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                img_array = np.array(img)
                height, width = img_array.shape[:2]
                
                logger.info(f"图片尺寸: {width} x {height}")
                
                # 转换为灰度
                gray = np.dot(img_array[...,:3], [0.2989, 0.5870, 0.1140])
                
                # 专门寻找水印文字的特征
                regions = self._find_watermark_text_regions(gray)
                
                logger.info(f"检测到 {len(regions)} 个水印文字区域")
                return regions
                
        except Exception as e:
            logger.error(f"检测失败 {image_path}: {e}")
            return []
    
    def _find_watermark_text_regions(self, gray):
        """
        高效寻找水印文字区域 - 使用分层检测
        """
        height, width = gray.shape
        regions = []

        logger.info("开始高效水印检测...")

        # 第一步：快速预筛选 - 寻找可能的水印区域
        candidate_regions = self._fast_candidate_detection(gray)
        logger.info(f"预筛选找到 {len(candidate_regions)} 个候选区域")

        # 第二步：精确验证每个候选区域
        for i, (x, y, w, h) in enumerate(candidate_regions):
            if x + w >= width or y + h >= height:
                continue

            window = gray[y:y+h, x:x+w]

            if self._is_watermark_text(window):
                regions.append((x, y, x + w, y + h))
                logger.info(f"确认水印区域 {i+1}: ({x}, {y}) 尺寸: {w}x{h}")

        # 去除重叠区域
        filtered_regions = self._remove_overlapping_regions(regions)

        return filtered_regions

    def _fast_candidate_detection(self, gray):
        """
        快速候选区域检测 - 基于灰度分布特征
        """
        height, width = gray.shape
        candidates = []

        # 使用更大的步长进行快速扫描
        step = 50  # 大步长
        window_size = 150  # 固定窗口大小

        for y in range(0, height - window_size, step):
            for x in range(0, width - window_size, step):
                window = gray[y:y+window_size, x:x+window_size]

                # 快速特征检测
                mean_val = np.mean(window)
                std_val = np.std(window)

                # 基于分析结果调整：这是一个很亮的图片
                # 水印可能是浅灰色，在白色背景上
                # 使用更宽松的条件
                if 200 < mean_val < 255 and 5 < std_val < 30:
                    # 进一步细分这个区域
                    sub_candidates = self._subdivide_region(gray, x, y, window_size)
                    candidates.extend(sub_candidates)

        return candidates

    def _subdivide_region(self, gray, start_x, start_y, size):
        """
        细分候选区域为更小的精确区域
        """
        candidates = []

        # 在候选区域内寻找更精确的水印文字
        sub_step = 20
        min_width, max_width = 80, 180
        min_height, max_height = 15, 35

        for y in range(start_y, start_y + size - max_height, sub_step):
            for x in range(start_x, start_x + size - max_width, sub_step):

                # 测试几个典型的水印文字尺寸
                for w in [100, 120, 150]:  # 典型宽度
                    for h in [20, 25, 30]:   # 典型高度
                        if (x + w <= start_x + size and
                            y + h <= start_y + size and
                            x + w < gray.shape[1] and
                            y + h < gray.shape[0]):

                            candidates.append((x, y, w, h))

        return candidates
    
    def _is_watermark_text(self, window):
        """
        判断窗口是否包含水印文字
        使用非常严格的条件
        """
        if window.size == 0:
            return False
        
        mean_val = np.mean(window)
        std_val = np.std(window)
        
        # 计算像素值分布
        unique_values = len(np.unique(window))
        
        # 水印文字的严格特征：
        # 1. 灰度值在特定范围（半透明水印的特征）
        # 2. 有适度的对比度（文字边缘）
        # 3. 不是纯色区域
        # 4. 不是高对比度区域（避免误检正常文字）
        
        # 基于实际分析调整的参数 - 这是一个很亮的图片
        # 使用更宽松的条件来捕获水印
        is_watermark_gray = 200 < mean_val < 255  # 很宽的灰度范围
        has_text_contrast = 5 < std_val < 25      # 宽松的对比度
        not_solid_color = unique_values > 3       # 不是纯色
        not_too_varied = std_val < 30             # 不是过度变化的区域

        # 额外检查：在亮背景上的水印特征
        # 计算不同灰度范围的像素比例
        very_light_pixels = np.sum(window > 240)
        medium_light_pixels = np.sum((window > 200) & (window <= 240))

        very_light_ratio = very_light_pixels / window.size
        medium_light_ratio = medium_light_pixels / window.size

        # 水印区域应该有混合的亮度
        has_mixed_brightness = very_light_ratio > 0.3 and medium_light_ratio > 0.1
        
        return (is_watermark_gray and
                has_text_contrast and
                not_solid_color and
                not_too_varied and
                has_mixed_brightness)
    
    def _remove_overlapping_regions(self, regions):
        """
        去除重叠区域，保留最佳的
        """
        if not regions:
            return []
        
        # 按面积排序，优先保留较小的区域（更可能是单个水印文字）
        regions = sorted(regions, key=lambda r: (r[2]-r[0]) * (r[3]-r[1]))
        
        filtered = []
        for region in regions:
            x1, y1, x2, y2 = region
            
            # 检查是否与已有区域重叠
            overlap = False
            for existing in filtered:
                ex1, ey1, ex2, ey2 = existing
                
                # 计算重叠
                overlap_x1 = max(x1, ex1)
                overlap_y1 = max(y1, ey1)
                overlap_x2 = min(x2, ex2)
                overlap_y2 = min(y2, ey2)
                
                if overlap_x1 < overlap_x2 and overlap_y1 < overlap_y2:
                    overlap_area = (overlap_x2 - overlap_x1) * (overlap_y2 - overlap_y1)
                    region_area = (x2 - x1) * (y2 - y1)
                    
                    if overlap_area / region_area > 0.2:  # 重叠超过20%
                        overlap = True
                        break
            
            if not overlap:
                filtered.append(region)
        
        return filtered
    
    def remove_watermark_minimal(self, image_path, output_path, regions):
        """
        最小化处理 - 只处理检测到的水印区域
        """
        try:
            with Image.open(image_path) as img:
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                logger.info(f"开始最小化处理 {len(regions)} 个水印区域")
                
                for i, region in enumerate(regions):
                    x1, y1, x2, y2 = region
                    
                    # 确保坐标有效
                    x1 = max(0, x1)
                    y1 = max(0, y1)
                    x2 = min(img.width, x2)
                    y2 = min(img.height, y2)
                    
                    if x2 <= x1 or y2 <= y1:
                        continue
                    
                    # 提取区域
                    roi = img.crop((x1, y1, x2, y2))
                    
                    # 非常轻微的处理 - 只是让水印变淡
                    # 1. 极轻微的模糊
                    blurred = roi.filter(ImageFilter.GaussianBlur(radius=0.8))
                    
                    # 2. 轻微提亮（让水印接近背景）
                    enhancer = ImageEnhance.Brightness(blurred)
                    brightened = enhancer.enhance(1.05)
                    
                    # 3. 轻微降低对比度
                    contrast_enhancer = ImageEnhance.Contrast(brightened)
                    final_roi = contrast_enhancer.enhance(0.95)
                    
                    # 粘贴回去
                    img.paste(final_roi, (x1, y1))
                    
                    logger.info(f"处理水印 {i+1}: ({x1}, {y1}, {x2}, {y2}) 尺寸: {x2-x1}x{y2-y1}")
                
                # 保存高质量结果
                img.save(output_path, quality=98)
                return True
                
        except Exception as e:
            logger.error(f"修复失败 {image_path}: {e}")
            return False
    
    def create_debug_image(self, image_path, regions, debug_path):
        """
        创建调试图像 - 只标记真正的水印
        """
        try:
            with Image.open(image_path) as img:
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                draw = ImageDraw.Draw(img)
                
                for i, region in enumerate(regions):
                    x1, y1, x2, y2 = region
                    # 绘制细红线边框
                    draw.rectangle([x1, y1, x2, y2], outline='red', width=2)
                    # 添加小标签
                    draw.text((x1, y1-15), f"WM{i+1}", fill='red')
                
                img.save(debug_path, quality=98)
                logger.info(f"调试图像已保存: {debug_path}")
                
        except Exception as e:
            logger.error(f"创建调试图像失败: {e}")
    
    def process_single_image(self, input_path, output_path, create_debug=True):
        """
        处理单张图片
        """
        try:
            logger.info(f"处理: {input_path}")
            
            # 精确检测水印文字
            regions = self.detect_watermark_text_only(input_path)
            
            if not regions:
                logger.info(f"未检测到水印文字，复制原图")
                import shutil
                shutil.copy2(input_path, output_path)
                return True
            
            # 创建调试图像
            if create_debug:
                debug_path = str(output_path).replace('.jpg', '_debug.jpg')
                self.create_debug_image(input_path, regions, debug_path)
            
            # 最小化去除水印
            success = self.remove_watermark_minimal(input_path, output_path, regions)
            
            if success:
                logger.info(f"处理完成: {output_path}")
            
            return success
            
        except Exception as e:
            logger.error(f"处理失败 {input_path}: {e}")
            return False

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='精确水印去除工具')
    parser.add_argument('--input', '-i', required=True, help='输入目录或文件')
    parser.add_argument('--output', '-o', required=True, help='输出目录或文件')
    parser.add_argument('--debug', action='store_true', help='创建调试图像')
    
    args = parser.parse_args()
    
    remover = PreciseWatermarkRemover()
    
    logger.info("🎯 精确水印去除工具启动")
    logger.info("=" * 50)
    
    input_path = Path(args.input)
    output_path = Path(args.output)
    
    if input_path.is_file():
        output_path.parent.mkdir(parents=True, exist_ok=True)
        remover.process_single_image(input_path, output_path, args.debug)
    else:
        output_path.mkdir(parents=True, exist_ok=True)
        
        supported_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
        image_files = []
        for ext in supported_formats:
            image_files.extend(input_path.glob(f"*{ext}"))
            image_files.extend(input_path.glob(f"*{ext.upper()}"))
        
        if not image_files:
            logger.error(f"在 {args.input} 中未找到支持的图片文件")
            return
        
        logger.info(f"找到 {len(image_files)} 个图片文件")
        
        for img_file in image_files:
            output_file = output_path / img_file.name
            remover.process_single_image(img_file, output_file, args.debug)

if __name__ == "__main__":
    main()
