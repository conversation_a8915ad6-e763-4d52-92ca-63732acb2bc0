#!/usr/bin/env python3
"""
超级优化水印去除工具
基于smart版本的进一步优化，专门针对文字水印
"""

import cv2
import numpy as np
import os
import argparse
from pathlib import Path
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class UltraWatermarkRemover:
    def __init__(self):
        self.supported_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
    
    def analyze_image_advanced(self, image):
        """高级图像分析"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 基础统计
        mean_brightness = np.mean(gray)
        std_brightness = np.std(gray)
        contrast = std_brightness / mean_brightness if mean_brightness > 0 else 0
        
        # 高级分析
        # 1. 检测图像的主要色调
        hist = cv2.calcHist([gray], [0], None, [256], [0, 256])
        dominant_brightness = np.argmax(hist)
        
        # 2. 检测纹理复杂度
        laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()
        
        # 3. 检测边缘密度
        edges = cv2.Canny(gray, 50, 150)
        edge_density = np.sum(edges > 0) / (edges.shape[0] * edges.shape[1])
        
        logger.info(f"高级分析 - 主色调: {dominant_brightness}, 纹理复杂度: {laplacian_var:.1f}, 边缘密度: {edge_density:.3f}")
        
        return {
            'mean_brightness': mean_brightness,
            'std_brightness': std_brightness,
            'contrast': contrast,
            'dominant_brightness': dominant_brightness,
            'texture_complexity': laplacian_var,
            'edge_density': edge_density
        }
    
    def detect_watermark_ultra(self, image):
        """超级水印检测算法"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        height, width = gray.shape
        
        # 高级图像分析
        img_stats = self.analyze_image_advanced(image)
        
        logger.info("开始超级水印检测...")
        
        # 动态调整参数
        if img_stats['texture_complexity'] > 1000:
            # 高纹理图像 - 更保守的检测
            brightness_tolerance = 15
            edge_threshold_low = 30
            edge_threshold_high = 90
        else:
            # 低纹理图像 - 更敏感的检测
            brightness_tolerance = 25
            edge_threshold_low = 20
            edge_threshold_high = 70
        
        # 方法1: 多层亮度检测
        mask1 = self._multi_level_brightness_detection(gray, img_stats, brightness_tolerance)
        
        # 方法2: 高级边缘检测
        mask2 = self._advanced_edge_detection(gray, edge_threshold_low, edge_threshold_high)
        
        # 方法3: 文字模式识别
        mask3 = self._text_pattern_recognition(gray)
        
        # 方法4: 频域分析
        mask4 = self._frequency_domain_analysis(gray)
        
        # 智能融合
        final_mask = self._ultra_smart_fusion(
            [mask1, mask2, mask3, mask4], 
            ['brightness', 'edges', 'text', 'frequency'],
            img_stats
        )
        
        # 超级后处理
        final_mask = self._ultra_post_process(final_mask, gray, img_stats)
        
        logger.info("超级水印检测完成")
        return final_mask
    
    def _multi_level_brightness_detection(self, gray, img_stats, tolerance):
        """多层亮度检测"""
        masks = []
        
        # 基于主色调的动态范围
        dominant = img_stats['dominant_brightness']
        
        # 多个检测范围
        ranges = [
            (dominant + 10, dominant + 30),
            (dominant + 20, dominant + 40),
            (180, 220),  # 经典范围
            (190, 230),  # 更亮范围
        ]
        
        for low, high in ranges:
            if low < 255 and high <= 255:
                low_val = int(max(0, low))
                high_val = int(min(255, high))
                mask = cv2.inRange(gray, low_val, high_val)
                masks.append(mask)
        
        # 组合多个范围
        combined = np.zeros_like(gray)
        for mask in masks:
            combined = cv2.bitwise_or(combined, mask)
        
        return combined
    
    def _advanced_edge_detection(self, gray, low_thresh, high_thresh):
        """高级边缘检测"""
        # 多种边缘检测方法
        
        # 1. 自适应Canny
        edges1 = cv2.Canny(gray, low_thresh, high_thresh)
        
        # 2. 基于梯度的边缘检测
        grad_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
        grad_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
        gradient_mag = np.sqrt(grad_x**2 + grad_y**2)
        gradient_mag = cv2.normalize(gradient_mag, None, 0, 255, cv2.NORM_MINMAX).astype(np.uint8)
        _, edges2 = cv2.threshold(gradient_mag, 30, 255, cv2.THRESH_BINARY)
        
        # 3. Laplacian边缘检测
        laplacian = cv2.Laplacian(gray, cv2.CV_64F)
        laplacian = np.absolute(laplacian).astype(np.uint8)
        _, edges3 = cv2.threshold(laplacian, 20, 255, cv2.THRESH_BINARY)
        
        # 组合边缘检测结果
        combined_edges = cv2.bitwise_or(cv2.bitwise_or(edges1, edges2), edges3)
        
        # 形态学处理连接文字
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
        combined_edges = cv2.morphologyEx(combined_edges, cv2.MORPH_CLOSE, kernel, iterations=2)
        
        return combined_edges
    
    def _text_pattern_recognition(self, gray):
        """文字模式识别"""
        # 使用MSER (Maximally Stable Extremal Regions) 检测文字区域
        mser = cv2.MSER_create()
        regions, _ = mser.detectRegions(gray)
        
        mask = np.zeros_like(gray)
        
        for region in regions:
            # 过滤掉太大或太小的区域
            if 50 < len(region) < 2000:
                # 计算区域的边界框
                hull = cv2.convexHull(region.reshape(-1, 1, 2))
                cv2.fillPoly(mask, [hull], 255)
        
        return mask
    
    def _frequency_domain_analysis(self, gray):
        """频域分析检测重复模式"""
        # FFT分析
        f_transform = np.fft.fft2(gray)
        f_shift = np.fft.fftshift(f_transform)
        magnitude_spectrum = np.log(np.abs(f_shift) + 1)
        
        # 检测重复模式的频率特征
        # 水印通常会在频谱中产生特定的模式
        
        # 简化的频域检测 - 检测高频成分
        rows, cols = gray.shape
        crow, ccol = rows // 2, cols // 2
        
        # 创建高通滤波器
        mask = np.ones((rows, cols), np.uint8)
        r = 30
        center = [crow, ccol]
        x, y = np.ogrid[:rows, :cols]
        mask_area = (x - center[0]) ** 2 + (y - center[1]) ** 2 <= r*r
        mask[mask_area] = 0
        
        # 应用滤波器
        f_shift_filtered = f_shift * mask
        f_ishift = np.fft.ifftshift(f_shift_filtered)
        img_back = np.fft.ifft2(f_ishift)
        img_back = np.abs(img_back)
        
        # 归一化并阈值化
        img_back = cv2.normalize(img_back, None, 0, 255, cv2.NORM_MINMAX).astype(np.uint8)
        _, freq_mask = cv2.threshold(img_back, 20, 255, cv2.THRESH_BINARY)
        
        return freq_mask
    
    def _ultra_smart_fusion(self, masks, mask_types, img_stats):
        """超级智能融合"""
        if not masks:
            return np.zeros((100, 100), dtype=np.uint8)
        
        # 根据图像特征动态调整权重
        weights = [0.4, 0.3, 0.2, 0.1]  # 默认权重
        
        # 根据纹理复杂度调整
        if img_stats['texture_complexity'] > 1000:
            # 高纹理 - 更依赖亮度检测
            weights = [0.5, 0.2, 0.2, 0.1]
        elif img_stats['edge_density'] > 0.1:
            # 高边缘密度 - 更依赖边缘检测
            weights = [0.3, 0.4, 0.2, 0.1]
        
        # 加权融合
        combined = np.zeros_like(masks[0], dtype=np.float32)
        for i, mask in enumerate(masks):
            if i < len(weights):
                combined += mask.astype(np.float32) * weights[i]
        
        # 自适应阈值
        threshold = 100
        if img_stats['contrast'] > 0.3:
            threshold = 80
        elif img_stats['contrast'] < 0.15:
            threshold = 120
        
        # 归一化并二值化
        combined = cv2.normalize(combined, None, 0, 255, cv2.NORM_MINMAX)
        _, combined = cv2.threshold(combined.astype(np.uint8), threshold, 255, cv2.THRESH_BINARY)
        
        return combined
    
    def _ultra_post_process(self, mask, gray, img_stats):
        """超级后处理"""
        # 基础清理
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel, iterations=1)
        
        # 连接断开区域
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
        mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel, iterations=2)
        
        # 根据图像特征调整后处理
        if img_stats['texture_complexity'] > 1000:
            # 高纹理图像 - 更保守的膨胀
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (2, 2))
            mask = cv2.dilate(mask, kernel, iterations=1)
        else:
            # 低纹理图像 - 更积极的膨胀
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
            mask = cv2.dilate(mask, kernel, iterations=2)
        
        # 质量检查和修正
        mask_area = np.sum(mask == 255)
        total_area = mask.shape[0] * mask.shape[1]
        mask_ratio = mask_area / total_area
        
        if mask_ratio > 0.4:
            logger.warning(f"遮罩过大 ({mask_ratio:.2%})，进行激进过滤")
            # 更激进的过滤
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (9, 9))
            mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel, iterations=3)
        
        return mask
    
    def remove_watermark_ultra(self, image, mask):
        """超级水印去除"""
        logger.info("开始超级水印去除...")
        
        # 检查遮罩质量
        mask_area = np.sum(mask == 255)
        total_area = mask.shape[0] * mask.shape[1]
        mask_ratio = mask_area / total_area
        
        # 根据遮罩大小选择策略
        if mask_ratio < 0.03:
            # 极小面积 - 精细修复
            result = cv2.inpaint(image, mask, 2, cv2.INPAINT_TELEA)
        elif mask_ratio < 0.1:
            # 小面积 - 标准修复
            result = cv2.inpaint(image, mask, 4, cv2.INPAINT_TELEA)
        elif mask_ratio < 0.25:
            # 中等面积 - 混合修复
            result1 = cv2.inpaint(image, mask, 6, cv2.INPAINT_TELEA)
            result2 = cv2.inpaint(image, mask, 6, cv2.INPAINT_NS)
            result = cv2.addWeighted(result1, 0.6, result2, 0.4, 0)
        else:
            # 大面积 - 大半径修复
            result = cv2.inpaint(image, mask, 8, cv2.INPAINT_NS)
        
        # 超级后处理
        result = self._ultra_enhance_result(result, image, mask)
        
        logger.info("超级水印去除完成")
        return result
    
    def _ultra_enhance_result(self, result, original, mask):
        """超级结果增强"""
        # 1. 边缘平滑
        mask_blur = cv2.GaussianBlur(mask.astype(np.float32), (21, 21), 0) / 255.0
        mask_blur = np.stack([mask_blur] * 3, axis=2)
        
        # 2. 多层混合
        # 第一层：基础混合
        blended = result.astype(np.float32) * mask_blur + original.astype(np.float32) * (1 - mask_blur)
        
        # 第二层：细节保持
        # 在修复区域边缘保持更多原始细节
        edge_mask = cv2.Canny(mask, 50, 150)
        edge_mask = cv2.dilate(edge_mask, np.ones((5, 5), np.uint8), iterations=1)
        edge_weight = cv2.GaussianBlur(edge_mask.astype(np.float32), (15, 15), 0) / 255.0
        edge_weight = np.stack([edge_weight] * 3, axis=2)
        
        # 在边缘区域混合更多原始图像
        final_result = blended * (1 - edge_weight * 0.3) + original.astype(np.float32) * (edge_weight * 0.3)
        
        # 3. 轻微的降噪和锐化
        final_result = final_result.astype(np.uint8)
        final_result = cv2.bilateralFilter(final_result, 9, 75, 75)
        
        return final_result
    
    def process_single_image(self, input_path, output_path, save_debug=False):
        """处理单张图片"""
        try:
            logger.info(f"开始超级处理: {input_path}")
            
            # 读取图片
            image = cv2.imread(str(input_path))
            if image is None:
                logger.error(f"无法读取图片: {input_path}")
                return False
            
            logger.info(f"图片尺寸: {image.shape}")
            
            # 超级检测水印
            mask = self.detect_watermark_ultra(image)
            
            # 检查遮罩质量
            mask_area = np.sum(mask == 255)
            total_area = mask.shape[0] * mask.shape[1]
            mask_ratio = mask_area / total_area
            
            logger.info(f"检测到的水印区域占比: {mask_ratio:.2%}")
            
            # 保存调试信息
            if save_debug:
                debug_dir = output_path.parent / "debug"
                debug_dir.mkdir(exist_ok=True)
                
                mask_path = debug_dir / f"{output_path.stem}_ultra_mask.png"
                cv2.imwrite(str(mask_path), mask)
                logger.info(f"遮罩已保存: {mask_path}")
            
            # 超级去除水印
            result = self.remove_watermark_ultra(image, mask)
            
            # 保存结果
            cv2.imwrite(str(output_path), result)
            logger.info(f"超级处理完成: {output_path}")
            
            return True
            
        except Exception as e:
            logger.error(f"处理图片时出错 {input_path}: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

def main():
    parser = argparse.ArgumentParser(description='超级优化水印去除工具')
    parser.add_argument('--input', '-i', required=True, help='输入图片文件或目录')
    parser.add_argument('--output', '-o', required=True, help='输出文件或目录')
    parser.add_argument('--debug', action='store_true', help='保存调试信息')
    
    args = parser.parse_args()
    
    remover = UltraWatermarkRemover()
    
    input_path = Path(args.input)
    output_path = Path(args.output)
    
    logger.info("🚀 超级优化水印去除工具启动")
    logger.info("=" * 50)
    
    if input_path.is_file():
        # 处理单个文件
        remover.process_single_image(input_path, output_path, args.debug)
    else:
        logger.error(f"输入路径不存在: {input_path}")

if __name__ == "__main__":
    main()
