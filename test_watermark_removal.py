#!/usr/bin/env python3
"""
测试水印去除工具的示例脚本
创建一个带有模拟水印的测试图片，然后使用我们的工具去除水印
"""

import cv2
import numpy as np
import os
from pathlib import Path

def create_test_image_with_watermark():
    """
    创建一个带有模拟水印的测试图片
    """
    # 创建一个彩色背景图片
    height, width = 400, 600
    image = np.zeros((height, width, 3), dtype=np.uint8)
    
    # 创建渐变背景
    for i in range(height):
        for j in range(width):
            image[i, j] = [
                int(255 * (i / height)),  # 红色渐变
                int(255 * (j / width)),   # 绿色渐变
                128                       # 固定蓝色
            ]
    
    # 添加一些内容（模拟真实图片）
    cv2.rectangle(image, (50, 50), (200, 150), (255, 255, 255), -1)
    cv2.putText(image, "Original Content", (60, 100), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
    
    cv2.circle(image, (400, 200), 50, (0, 255, 255), -1)
    cv2.putText(image, "Important", (360, 210), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)
    
    # 添加模拟水印 (类似 www.abskoop.com 的重复水印)
    watermark_text = "www.example.com"
    font = cv2.FONT_HERSHEY_SIMPLEX
    font_scale = 0.6
    color = (220, 220, 220)  # 浅灰色水印
    thickness = 1
    
    # 在多个位置添加水印
    positions = [
        (100, 100), (300, 150), (150, 250), (400, 300),
        (50, 350), (350, 100), (200, 200), (450, 250)
    ]
    
    for pos in positions:
        cv2.putText(image, watermark_text, pos, font, font_scale, color, thickness)
    
    return image

def main():
    """
    主函数：创建测试图片并演示水印去除
    """
    print("🎨 创建带水印的测试图片...")
    
    # 确保目录存在
    input_dir = Path("input_images")
    output_dir = Path("output_images")
    input_dir.mkdir(exist_ok=True)
    output_dir.mkdir(exist_ok=True)
    
    # 创建测试图片
    test_image = create_test_image_with_watermark()
    test_image_path = input_dir / "test_with_watermark.jpg"
    
    # 保存测试图片
    cv2.imwrite(str(test_image_path), test_image)
    print(f"✅ 测试图片已保存到: {test_image_path}")
    
    print("\n📋 现在你可以运行以下命令来测试水印去除：")
    print("\n1. 激活虚拟环境：")
    print("   source lama_env/bin/activate")
    
    print("\n2. 处理单张图片：")
    print(f"   python watermark_remover.py -i {test_image_path} -o output_images/cleaned_test.jpg")
    
    print("\n3. 批量处理（如果有多张图片）：")
    print("   python watermark_remover.py -i input_images -o output_images")
    
    print("\n4. 使用文字水印专用模式：")
    print("   python watermark_remover.py -i input_images -o output_images --mask-method text")
    
    print("\n🔍 处理完成后，检查以下文件：")
    print("   - output_images/removed_test_with_watermark.jpg (去除水印后的图片)")
    print("   - output_images/removed_test_with_watermark_mask.jpg (生成的遮罩，用于调试)")
    
    print("\n💡 提示：")
    print("   - 将你的真实图片放入 input_images/ 目录")
    print("   - 运行批量处理命令")
    print("   - 在 output_images/ 目录查看结果")

if __name__ == "__main__":
    main()
