#!/usr/bin/env python3
"""
专门针对 abskoop.com 类型水印的去除工具
简化但更有效的方法
"""

import cv2
import numpy as np
import os
import argparse
from pathlib import Path
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FocusedWatermarkRemover:
    def __init__(self):
        self.supported_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
    
    def detect_abskoop_watermark(self, image):
        """
        专门检测 abskoop.com 类型的半透明水印
        更精确的检测算法
        """
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        height, width = gray.shape

        logger.info("开始检测 abskoop 水印...")

        # 方法1: 更精确的亮度范围检测
        # 使用更窄的范围和多层检测
        mask1 = cv2.inRange(gray, 185, 210)  # 缩小范围

        # 方法2: 检测文字特征
        # 使用更保守的边缘检测
        edges = cv2.Canny(gray, 30, 80)

        # 只保留可能是文字的边缘
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
        edges_cleaned = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel, iterations=1)

        # 方法3: 检测重复模式
        mask3 = self._detect_repetitive_text(gray)

        # 更智能的组合策略
        # 只有当多个方法都检测到时才认为是水印
        intersection_mask = cv2.bitwise_and(mask1, edges_cleaned)

        # 与重复文字检测结果取交集
        if np.sum(mask3) > 0:
            intersection_mask = cv2.bitwise_and(intersection_mask, mask3)

        # 如果交集太小，回退到亮度检测
        intersection_area = np.sum(intersection_mask == 255)
        total_area = height * width

        if intersection_area / total_area < 0.01:  # 如果交集太小
            logger.info("交集太小，使用亮度检测结果")
            final_mask = mask1
        else:
            logger.info("使用多方法交集结果")
            final_mask = intersection_mask

        # 后处理
        final_mask = self._clean_mask(final_mask)

        logger.info("水印检测完成")
        return final_mask
    
    def _detect_repetitive_text(self, gray):
        """检测重复的文字模式 - 更保守的方法"""
        height, width = gray.shape
        mask = np.zeros_like(gray)

        # 使用更保守的阈值检测
        # 只检测明显的文字边缘
        _, binary = cv2.threshold(gray, 190, 255, cv2.THRESH_BINARY)

        # 查找轮廓
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # 更严格的文字特征过滤
        text_contours = []
        for contour in contours:
            area = cv2.contourArea(contour)
            x, y, w, h = cv2.boundingRect(contour)
            aspect_ratio = w / h if h > 0 else 0

            # 更严格的文字特征：
            # 1. 面积适中（不要太大的区域）
            # 2. 宽高比合理（文字特征）
            # 3. 不要太小的噪声点
            if 100 < area < 1000 and 1 < aspect_ratio < 4 and w > 10 and h > 5:
                text_contours.append(contour)

        # 只有检测到足够的文字轮廓才认为有效
        if len(text_contours) >= 3:  # 至少要有3个文字区域
            for contour in text_contours:
                cv2.fillPoly(mask, [contour], 255)

        return mask
    
    def _clean_mask(self, mask):
        """清理和优化遮罩"""
        # 去除小的噪声点
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel, iterations=1)
        
        # 连接断开的区域
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
        mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel, iterations=2)
        
        # 轻微膨胀以确保覆盖水印边缘
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        mask = cv2.dilate(mask, kernel, iterations=1)
        
        return mask
    
    def remove_watermark_simple(self, image, mask):
        """
        简化但有效的水印去除
        使用最佳的OpenCV inpainting方法
        """
        logger.info("开始去除水印...")
        
        # 使用Telea算法，它对文字水印效果较好
        result = cv2.inpaint(image, mask, 3, cv2.INPAINT_TELEA)
        
        # 可选：再次使用NS算法进行精细修复
        # result = cv2.inpaint(result, mask, 3, cv2.INPAINT_NS)
        
        logger.info("水印去除完成")
        return result
    
    def enhance_result(self, image, original):
        """
        增强处理结果
        """
        # 轻微的高斯模糊以平滑修复区域
        blurred = cv2.GaussianBlur(image, (3, 3), 0)
        
        # 使用双边滤波保持边缘的同时平滑
        enhanced = cv2.bilateralFilter(blurred, 9, 75, 75)
        
        return enhanced
    
    def process_single_image(self, input_path, output_path, save_debug=False):
        """处理单张图片"""
        try:
            logger.info(f"开始处理: {input_path}")
            
            # 读取图片
            image = cv2.imread(str(input_path))
            if image is None:
                logger.error(f"无法读取图片: {input_path}")
                return False
            
            logger.info(f"图片尺寸: {image.shape}")
            
            # 检测水印
            mask = self.detect_abskoop_watermark(image)
            
            # 检查遮罩是否有效
            mask_area = np.sum(mask == 255)
            total_area = mask.shape[0] * mask.shape[1]
            mask_ratio = mask_area / total_area
            
            logger.info(f"检测到的水印区域占比: {mask_ratio:.2%}")
            
            if mask_ratio < 0.001:
                logger.warning("检测到的水印区域很小，可能检测不准确")
            elif mask_ratio > 0.5:
                logger.warning("检测到的水印区域过大，可能误检")
            
            # 保存调试信息
            if save_debug:
                debug_dir = output_path.parent / "debug"
                debug_dir.mkdir(exist_ok=True)
                
                mask_path = debug_dir / f"{output_path.stem}_mask.png"
                cv2.imwrite(str(mask_path), mask)
                logger.info(f"遮罩已保存: {mask_path}")
            
            # 去除水印
            result = self.remove_watermark_simple(image, mask)
            
            # 增强结果
            enhanced_result = self.enhance_result(result, image)
            
            # 保存结果
            cv2.imwrite(str(output_path), enhanced_result)
            logger.info(f"处理完成: {output_path}")
            
            return True
            
        except Exception as e:
            logger.error(f"处理图片时出错 {input_path}: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
    
    def batch_process(self, input_dir, output_dir, save_debug=False):
        """批量处理图片"""
        input_path = Path(input_dir)
        output_path = Path(output_dir)
        
        # 创建输出目录
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 获取所有支持的图片文件
        image_files = []
        for ext in self.supported_formats:
            image_files.extend(input_path.glob(f"*{ext}"))
            image_files.extend(input_path.glob(f"*{ext.upper()}"))
        
        if not image_files:
            logger.warning(f"在 {input_dir} 中没有找到支持的图片文件")
            return
        
        logger.info(f"找到 {len(image_files)} 个图片文件")
        
        success_count = 0
        for i, img_file in enumerate(image_files, 1):
            logger.info(f"处理进度: {i}/{len(image_files)}")
            output_file = output_path / f"focused_{img_file.name}"
            
            if self.process_single_image(img_file, output_file, save_debug):
                success_count += 1
        
        logger.info(f"批量处理完成: {success_count}/{len(image_files)} 个文件处理成功")

def main():
    parser = argparse.ArgumentParser(description='专门针对abskoop水印的去除工具')
    parser.add_argument('--input', '-i', required=True, help='输入图片文件或目录')
    parser.add_argument('--output', '-o', required=True, help='输出文件或目录')
    parser.add_argument('--debug', action='store_true', help='保存调试信息')
    
    args = parser.parse_args()
    
    remover = FocusedWatermarkRemover()
    
    input_path = Path(args.input)
    output_path = Path(args.output)
    
    logger.info("🎯 专门的abskoop水印去除工具启动")
    logger.info("=" * 50)
    
    if input_path.is_file():
        # 处理单个文件
        remover.process_single_image(input_path, output_path, args.debug)
    elif input_path.is_dir():
        # 批量处理
        remover.batch_process(input_path, output_path, args.debug)
    else:
        logger.error(f"输入路径不存在: {input_path}")

if __name__ == "__main__":
    main()
