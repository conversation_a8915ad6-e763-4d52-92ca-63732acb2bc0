#!/usr/bin/env python3
"""
倾斜水印检测和去除工具
专门针对倾斜的 WWW.ABSKOOP.COM 水印
"""

import os
import sys
from pathlib import Path
import logging
import math

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

try:
    from PIL import Image, ImageFilter, ImageEnhance, ImageDraw, ImageFont
    import numpy as np
except ImportError:
    logger.error("需要安装 Pillow 和 numpy: pip install Pillow numpy")
    sys.exit(1)

class RotatedWatermarkRemover:
    def __init__(self):
        self.target_text = "WWW.ABSKOOP.COM"
        self.processed_count = 0
        
    def detect_rotated_watermarks(self, image_path):
        """
        检测倾斜的水印文字
        """
        try:
            with Image.open(image_path) as img:
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                img_array = np.array(img)
                height, width = img_array.shape[:2]
                
                logger.info(f"图片尺寸: {width} x {height}")
                
                # 转换为灰度
                gray = np.dot(img_array[...,:3], [0.2989, 0.5870, 0.1140])
                
                # 检测不同角度的水印
                angles_to_test = [-45, -30, -15, 0, 15, 30, 45]  # 测试不同倾斜角度
                all_regions = []
                
                for angle in angles_to_test:
                    regions = self._detect_at_angle(gray, angle)
                    all_regions.extend(regions)
                    if regions:
                        logger.info(f"在角度 {angle}° 检测到 {len(regions)} 个区域")
                
                # 去重和过滤
                filtered_regions = self._filter_and_merge_regions(all_regions)
                
                logger.info(f"最终检测到 {len(filtered_regions)} 个水印区域")
                return filtered_regions
                
        except Exception as e:
            logger.error(f"检测失败 {image_path}: {e}")
            return []
    
    def _detect_at_angle(self, gray, angle):
        """
        在特定角度检测水印
        """
        height, width = gray.shape
        regions = []
        
        # 计算旋转后的扫描方向
        angle_rad = math.radians(angle)
        cos_a = math.cos(angle_rad)
        sin_a = math.sin(angle_rad)
        
        # 扫描参数
        scan_step = 20
        window_width = 200  # 水印文字的大概宽度
        window_height = 30  # 水印文字的大概高度
        
        # 沿着倾斜方向扫描
        for start_y in range(0, height - window_height, scan_step):
            for start_x in range(0, width - window_width, scan_step):
                
                # 提取倾斜窗口的像素
                pixels = self._extract_rotated_window(gray, start_x, start_y, 
                                                    window_width, window_height, angle)
                
                if pixels is None or len(pixels) == 0:
                    continue
                
                # 检查是否符合水印特征
                if self._is_watermark_pattern(pixels):
                    # 计算实际的边界框
                    region = self._calculate_rotated_bbox(start_x, start_y, 
                                                        window_width, window_height, angle)
                    if region:
                        regions.append(region)
        
        return regions
    
    def _extract_rotated_window(self, gray, start_x, start_y, width, height, angle):
        """
        提取旋转窗口的像素
        """
        angle_rad = math.radians(angle)
        cos_a = math.cos(angle_rad)
        sin_a = math.sin(angle_rad)
        
        pixels = []
        img_height, img_width = gray.shape
        
        # 采样旋转窗口内的像素
        for dy in range(0, height, 2):  # 每隔2像素采样
            for dx in range(0, width, 2):
                # 计算旋转后的坐标
                rotated_x = start_x + dx * cos_a - dy * sin_a
                rotated_y = start_y + dx * sin_a + dy * cos_a
                
                # 检查边界
                if (0 <= rotated_x < img_width and 0 <= rotated_y < img_height):
                    x_int, y_int = int(rotated_x), int(rotated_y)
                    pixels.append(gray[y_int, x_int])
        
        return np.array(pixels) if pixels else None
    
    def _is_watermark_pattern(self, pixels):
        """
        检查像素是否符合水印模式
        """
        if len(pixels) < 10:
            return False
        
        mean_val = np.mean(pixels)
        std_val = np.std(pixels)
        
        # 水印特征：
        # 1. 中等灰度（不是纯白背景，也不是深色正文）
        # 2. 有一定的对比度变化（文字边缘）
        # 3. 不是纯色区域
        
        return (170 < mean_val < 210 and  # 水印的灰度范围
                5 < std_val < 25 and      # 有适度的对比度变化
                len(np.unique(pixels)) > 3)  # 不是纯色
    
    def _calculate_rotated_bbox(self, start_x, start_y, width, height, angle):
        """
        计算旋转矩形的边界框
        """
        angle_rad = math.radians(angle)
        cos_a = math.cos(angle_rad)
        sin_a = math.sin(angle_rad)
        
        # 矩形的四个角点
        corners = [
            (0, 0),
            (width, 0),
            (width, height),
            (0, height)
        ]
        
        # 旋转并平移
        rotated_corners = []
        for dx, dy in corners:
            x = start_x + dx * cos_a - dy * sin_a
            y = start_y + dx * sin_a + dy * cos_a
            rotated_corners.append((x, y))
        
        # 计算边界框
        xs = [corner[0] for corner in rotated_corners]
        ys = [corner[1] for corner in rotated_corners]
        
        min_x, max_x = int(min(xs)), int(max(xs))
        min_y, max_y = int(min(ys)), int(max(ys))
        
        return (min_x, min_y, max_x, max_y)
    
    def _filter_and_merge_regions(self, regions):
        """
        过滤和合并重叠区域
        """
        if not regions:
            return []
        
        # 去除重叠区域
        filtered = []
        for region in regions:
            x1, y1, x2, y2 = region
            
            # 检查是否与现有区域重叠
            overlap = False
            for existing in filtered:
                ex1, ey1, ex2, ey2 = existing
                
                # 计算重叠面积
                overlap_x1 = max(x1, ex1)
                overlap_y1 = max(y1, ey1)
                overlap_x2 = min(x2, ex2)
                overlap_y2 = min(y2, ey2)
                
                if overlap_x1 < overlap_x2 and overlap_y1 < overlap_y2:
                    overlap_area = (overlap_x2 - overlap_x1) * (overlap_y2 - overlap_y1)
                    region_area = (x2 - x1) * (y2 - y1)
                    
                    if overlap_area / region_area > 0.3:  # 重叠超过30%
                        overlap = True
                        break
            
            if not overlap:
                filtered.append(region)
        
        return filtered
    
    def remove_watermark_precise(self, image_path, output_path, regions):
        """
        精确去除水印
        """
        try:
            with Image.open(image_path) as img:
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                logger.info(f"开始处理 {len(regions)} 个水印区域")
                
                for i, region in enumerate(regions):
                    x1, y1, x2, y2 = region
                    
                    # 确保坐标在图片范围内
                    x1 = max(0, x1)
                    y1 = max(0, y1)
                    x2 = min(img.width, x2)
                    y2 = min(img.height, y2)
                    
                    if x2 <= x1 or y2 <= y1:
                        continue
                    
                    # 提取区域
                    roi = img.crop((x1, y1, x2, y2))
                    
                    # 温和处理
                    # 1. 轻微模糊
                    blurred = roi.filter(ImageFilter.GaussianBlur(radius=1.5))
                    
                    # 2. 提亮（让水印接近背景）
                    enhancer = ImageEnhance.Brightness(blurred)
                    brightened = enhancer.enhance(1.15)
                    
                    # 3. 降低对比度
                    contrast_enhancer = ImageEnhance.Contrast(brightened)
                    final_roi = contrast_enhancer.enhance(0.85)
                    
                    # 粘贴回去
                    img.paste(final_roi, (x1, y1))
                    
                    logger.info(f"处理区域 {i+1}: ({x1}, {y1}, {x2}, {y2})")
                
                # 保存结果
                img.save(output_path, quality=95)
                return True
                
        except Exception as e:
            logger.error(f"修复失败 {image_path}: {e}")
            return False
    
    def create_debug_image(self, image_path, regions, debug_path):
        """
        创建调试图像
        """
        try:
            with Image.open(image_path) as img:
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                draw = ImageDraw.Draw(img)
                
                for i, region in enumerate(regions):
                    x1, y1, x2, y2 = region
                    # 绘制红色边框
                    draw.rectangle([x1, y1, x2, y2], outline='red', width=3)
                    # 添加编号
                    draw.text((x1, y1-20), f"W{i+1}", fill='red')
                
                img.save(debug_path, quality=95)
                logger.info(f"调试图像已保存: {debug_path}")
                
        except Exception as e:
            logger.error(f"创建调试图像失败: {e}")
    
    def process_single_image(self, input_path, output_path, create_debug=True):
        """
        处理单张图片
        """
        try:
            logger.info(f"处理: {input_path}")
            
            # 检测倾斜水印
            regions = self.detect_rotated_watermarks(input_path)
            
            if not regions:
                logger.info(f"未检测到目标水印，复制原图")
                import shutil
                shutil.copy2(input_path, output_path)
                return True
            
            # 创建调试图像
            if create_debug:
                debug_path = str(output_path).replace('.jpg', '_debug.jpg')
                self.create_debug_image(input_path, regions, debug_path)
            
            # 去除水印
            success = self.remove_watermark_precise(input_path, output_path, regions)
            
            if success:
                logger.info(f"处理完成: {output_path}")
            
            return success
            
        except Exception as e:
            logger.error(f"处理失败 {input_path}: {e}")
            return False

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='倾斜水印检测和去除工具')
    parser.add_argument('--input', '-i', required=True, help='输入目录或文件')
    parser.add_argument('--output', '-o', required=True, help='输出目录或文件')
    parser.add_argument('--debug', action='store_true', help='创建调试图像')
    
    args = parser.parse_args()
    
    remover = RotatedWatermarkRemover()
    
    logger.info("🎯 倾斜水印检测和去除工具启动")
    logger.info("=" * 50)
    
    input_path = Path(args.input)
    output_path = Path(args.output)
    
    if input_path.is_file():
        output_path.parent.mkdir(parents=True, exist_ok=True)
        remover.process_single_image(input_path, output_path, args.debug)
    else:
        output_path.mkdir(parents=True, exist_ok=True)
        
        supported_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
        image_files = []
        for ext in supported_formats:
            image_files.extend(input_path.glob(f"*{ext}"))
            image_files.extend(input_path.glob(f"*{ext.upper()}"))
        
        if not image_files:
            logger.error(f"在 {args.input} 中未找到支持的图片文件")
            return
        
        logger.info(f"找到 {len(image_files)} 个图片文件")
        
        for img_file in image_files:
            output_file = output_path / img_file.name
            remover.process_single_image(img_file, output_file, args.debug)

if __name__ == "__main__":
    main()
