#!/usr/bin/env python3
"""
全自动水印去除工具
基于学习到的水印特征，自动检测和去除所有水印
"""

import os
import sys
from pathlib import Path
import logging
import numpy as np
from PIL import Image, ImageFilter, ImageEnhance, ImageDraw

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AutoWatermarkRemover:
    def __init__(self):
        # 基于之前分析的水印特征
        self.watermark_patterns = [
            "WWW.ABSKOOP.COM",
            "ABSKOOP.COM", 
            "WWW.ABSKOOP",
            "ABSKOOP",
            "WWW.NERDSBITS.COM",
            "NERDSBITS.COM"
        ]
        
    def detect_all_watermark_regions(self, image_path):
        """
        自动检测图片中所有可能的水印区域
        """
        try:
            with Image.open(image_path) as img:
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                img_array = np.array(img)
                height, width = img_array.shape[:2]
                
                logger.info(f"分析图片: {width} x {height}")
                
                # 转换为灰度
                gray = np.dot(img_array[...,:3], [0.2989, 0.5870, 0.1140])
                
                # 寻找水印区域
                regions = self._scan_for_watermarks(gray, width, height)
                
                logger.info(f"自动检测到 {len(regions)} 个水印区域")
                return regions
                
        except Exception as e:
            logger.error(f"检测失败 {image_path}: {e}")
            return []
    
    def _scan_for_watermarks(self, gray, width, height):
        """
        扫描图片寻找水印特征
        """
        regions = []
        
        # 基于之前学到的特征：
        # 1. 水印通常是浅灰色 (200-250)
        # 2. 有适度的对比度变化 (std > 10)
        # 3. 尺寸通常在 80-200 像素宽，15-40 像素高
        
        # 使用滑动窗口扫描
        window_sizes = [
            (120, 25),  # 典型水印尺寸
            (150, 30),
            (100, 20),
            (180, 35)
        ]
        
        step = 30  # 扫描步长
        
        for w, h in window_sizes:
            for y in range(0, height - h, step):
                for x in range(0, width - w, step):
                    window = gray[y:y+h, x:x+w]
                    
                    if self._is_watermark_region(window):
                        # 避免重叠区域
                        if not self._overlaps_existing(regions, (x, y, x+w, y+h)):
                            regions.append((x, y, x+w, y+h))
        
        # 合并相近的区域
        merged_regions = self._merge_nearby_regions(regions)
        
        return merged_regions
    
    def _is_watermark_region(self, window):
        """
        判断窗口是否包含水印
        使用更严格的特征匹配
        """
        if window.size == 0:
            return False

        mean_val = np.mean(window)
        std_val = np.std(window)

        # 更严格的水印特征：
        # 1. 平均灰度在特定范围（更窄的范围）
        is_watermark_gray = 220 < mean_val < 245

        # 2. 对比度变化在特定范围（水印通常对比度较低）
        has_watermark_contrast = 10 < std_val < 20

        # 3. 不是高对比度区域（排除按钮、图标等）
        not_high_contrast = std_val < 22

        # 4. 检查灰度分布的均匀性
        # 水印通常有相对均匀的灰度分布
        hist, _ = np.histogram(window.flatten(), bins=20, range=(200, 250))
        max_bin_ratio = np.max(hist) / window.size
        is_uniform_distribution = max_bin_ratio < 0.4  # 没有过于集中的灰度值

        # 5. 检查边缘密度（水印边缘通常较少）
        # 计算梯度
        gy, gx = np.gradient(window)
        gradient_magnitude = np.sqrt(gx**2 + gy**2)
        edge_density = np.sum(gradient_magnitude > 5) / window.size
        low_edge_density = edge_density < 0.15  # 边缘密度较低

        # 6. 排除明显的界面元素
        # 检查是否有明显的几何形状（如矩形边框）
        # 如果四周有明显的边界，可能是按钮或界面元素
        top_edge = np.mean(np.abs(np.diff(window[0, :])))
        bottom_edge = np.mean(np.abs(np.diff(window[-1, :])))
        left_edge = np.mean(np.abs(np.diff(window[:, 0])))
        right_edge = np.mean(np.abs(np.diff(window[:, -1])))

        avg_edge_change = (top_edge + bottom_edge + left_edge + right_edge) / 4
        not_ui_element = avg_edge_change < 10  # 边缘变化不大

        # 7. 尺寸合理性检查
        height, width = window.shape
        reasonable_aspect_ratio = 2 < width/height < 15  # 水印通常是横向的

        return (is_watermark_gray and
                has_watermark_contrast and
                not_high_contrast and
                is_uniform_distribution and
                low_edge_density and
                not_ui_element and
                reasonable_aspect_ratio)
    
    def _overlaps_existing(self, regions, new_region):
        """
        检查新区域是否与现有区域重叠
        """
        x1, y1, x2, y2 = new_region
        
        for existing in regions:
            ex1, ey1, ex2, ey2 = existing
            
            # 计算重叠
            overlap_x1 = max(x1, ex1)
            overlap_y1 = max(y1, ey1)
            overlap_x2 = min(x2, ex2)
            overlap_y2 = min(y2, ey2)
            
            if overlap_x1 < overlap_x2 and overlap_y1 < overlap_y2:
                overlap_area = (overlap_x2 - overlap_x1) * (overlap_y2 - overlap_y1)
                new_area = (x2 - x1) * (y2 - y1)
                
                if overlap_area / new_area > 0.3:  # 重叠超过30%
                    return True
        
        return False
    
    def _merge_nearby_regions(self, regions):
        """
        合并相近的区域
        """
        if not regions:
            return []
        
        merged = []
        used = set()
        
        for i, region1 in enumerate(regions):
            if i in used:
                continue
            
            x1, y1, x2, y2 = region1
            
            # 寻找相近的区域
            for j, region2 in enumerate(regions[i+1:], i+1):
                if j in used:
                    continue
                
                ex1, ey1, ex2, ey2 = region2
                
                # 如果两个区域很近，合并它们
                distance = min(
                    abs(x1 - ex2), abs(x2 - ex1),  # 水平距离
                    abs(y1 - ey2), abs(y2 - ey1)   # 垂直距离
                )
                
                if distance < 50:  # 距离小于50像素
                    # 合并区域
                    x1 = min(x1, ex1)
                    y1 = min(y1, ey1)
                    x2 = max(x2, ex2)
                    y2 = max(y2, ey2)
                    used.add(j)
            
            merged.append((x1, y1, x2, y2))
            used.add(i)
        
        return merged
    
    def remove_watermarks_auto(self, image_path, output_path, create_debug=True):
        """
        自动去除所有水印
        """
        try:
            # 检测水印区域
            regions = self.detect_all_watermark_regions(image_path)
            
            if not regions:
                logger.info("未检测到水印，复制原图")
                import shutil
                shutil.copy2(image_path, output_path)
                return True
            
            # 处理图片
            with Image.open(image_path) as img:
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                logger.info(f"开始处理 {len(regions)} 个水印区域")
                
                for i, region in enumerate(regions):
                    x1, y1, x2, y2 = region
                    
                    # 确保坐标有效
                    x1 = max(0, x1)
                    y1 = max(0, y1)
                    x2 = min(img.width, x2)
                    y2 = min(img.height, y2)
                    
                    if x2 <= x1 or y2 <= y1:
                        continue
                    
                    # 提取并处理区域
                    roi = img.crop((x1, y1, x2, y2))
                    processed_roi = self._process_watermark_region(roi)
                    
                    # 粘贴回去
                    img.paste(processed_roi, (x1, y1))
                    
                    logger.info(f"处理水印 {i+1}: ({x1}, {y1}, {x2}, {y2}) 尺寸: {x2-x1}x{y2-y1}")
                
                # 保存结果
                img.save(output_path, quality=98)
                
                # 创建调试图像
                if create_debug:
                    debug_path = str(output_path).replace('.jpg', '_debug.jpg')
                    self._create_debug_image(image_path, regions, debug_path)
                
                logger.info(f"自动处理完成: {output_path}")
                return True
                
        except Exception as e:
            logger.error(f"自动处理失败 {image_path}: {e}")
            return False
    
    def _process_watermark_region(self, roi):
        """
        处理单个水印区域
        """
        # 强力处理方法
        # 1. 强模糊
        blurred = roi.filter(ImageFilter.GaussianBlur(radius=2.5))
        
        # 2. 大幅提高亮度
        enhancer = ImageEnhance.Brightness(blurred)
        brightened = enhancer.enhance(1.20)
        
        # 3. 降低对比度
        contrast_enhancer = ImageEnhance.Contrast(brightened)
        low_contrast = contrast_enhancer.enhance(0.80)
        
        # 4. 降低饱和度
        color_enhancer = ImageEnhance.Color(low_contrast)
        final_roi = color_enhancer.enhance(0.92)
        
        return final_roi
    
    def _create_debug_image(self, original_path, regions, debug_path):
        """
        创建调试图像，显示检测到的水印区域
        """
        try:
            with Image.open(original_path) as img:
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                draw = ImageDraw.Draw(img)
                
                for i, region in enumerate(regions):
                    x1, y1, x2, y2 = region
                    # 绘制红色边框
                    draw.rectangle([x1, y1, x2, y2], outline='red', width=3)
                    # 添加标签
                    draw.text((x1, y1-20), f"水印{i+1}", fill='red')
                
                img.save(debug_path, quality=98)
                logger.info(f"调试图像已保存: {debug_path}")
                
        except Exception as e:
            logger.error(f"创建调试图像失败: {e}")

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='全自动水印去除工具')
    parser.add_argument('--input', '-i', required=True, help='输入目录或文件')
    parser.add_argument('--output', '-o', required=True, help='输出目录或文件')
    parser.add_argument('--debug', action='store_true', help='创建调试图像')
    
    args = parser.parse_args()
    
    remover = AutoWatermarkRemover()
    
    logger.info("🤖 全自动水印去除工具启动")
    logger.info("=" * 50)
    
    input_path = Path(args.input)
    output_path = Path(args.output)
    
    if input_path.is_file():
        # 处理单个文件
        output_path.parent.mkdir(parents=True, exist_ok=True)
        remover.remove_watermarks_auto(input_path, output_path, args.debug)
    else:
        # 批量处理
        output_path.mkdir(parents=True, exist_ok=True)
        
        supported_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
        image_files = []
        for ext in supported_formats:
            image_files.extend(input_path.glob(f"*{ext}"))
            image_files.extend(input_path.glob(f"*{ext.upper()}"))
        
        if not image_files:
            logger.error(f"在 {args.input} 中未找到支持的图片文件")
            return
        
        logger.info(f"找到 {len(image_files)} 个图片文件")
        
        for img_file in image_files:
            output_file = output_path / img_file.name
            remover.remove_watermarks_auto(img_file, output_file, args.debug)

if __name__ == "__main__":
    main()
