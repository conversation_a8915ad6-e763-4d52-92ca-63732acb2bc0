#!/usr/bin/env python3
"""
改进的水印去除工具
针对检测到的问题进行优化
"""

import cv2
import numpy as np
import os
import argparse
from pathlib import Path
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ImprovedWatermarkRemover:
    def __init__(self):
        self.supported_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
    
    def detect_watermark_improved(self, image):
        """
        改进的水印检测算法
        专门针对 www.abskoop.com 类型的半透明重复水印
        """
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        height, width = gray.shape
        
        # 方法1: 更精确的亮度检测
        mask1 = self._detect_by_brightness_improved(gray)
        
        # 方法2: 基于文字特征的检测
        mask2 = self._detect_text_features(gray)
        
        # 方法3: 基于重复模式的检测
        mask3 = self._detect_repetitive_pattern(gray)
        
        # 方法4: 基于对比度变化的检测
        mask4 = self._detect_contrast_changes(gray)
        
        # 组合所有检测结果
        combined_mask = self._smart_combine_masks([mask1, mask2, mask3, mask4])
        
        # 后处理优化
        final_mask = self._post_process_mask(combined_mask, gray)
        
        return final_mask
    
    def _detect_by_brightness_improved(self, gray):
        """改进的亮度检测"""
        # 多阈值检测
        masks = []
        
        # 检测不同亮度范围的水印
        thresholds = [
            (160, 200),  # 较暗的半透明水印
            (180, 220),  # 中等亮度水印
            (200, 240),  # 较亮的水印
        ]
        
        for low, high in thresholds:
            mask = cv2.inRange(gray, low, high)
            # 形态学操作
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
            mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel, iterations=2)
            masks.append(mask)
        
        # 合并不同阈值的结果
        combined = np.zeros_like(gray)
        for mask in masks:
            combined = cv2.bitwise_or(combined, mask)
        
        return combined
    
    def _detect_text_features(self, gray):
        """基于文字特征的检测"""
        # 使用多种边缘检测方法
        
        # Canny边缘检测
        edges1 = cv2.Canny(gray, 30, 100)
        
        # Sobel边缘检测
        sobelx = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
        sobely = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
        edges2 = np.sqrt(sobelx**2 + sobely**2).astype(np.uint8)
        edges2 = cv2.threshold(edges2, 50, 255, cv2.THRESH_BINARY)[1]
        
        # Laplacian边缘检测
        laplacian = cv2.Laplacian(gray, cv2.CV_64F)
        edges3 = np.absolute(laplacian).astype(np.uint8)
        edges3 = cv2.threshold(edges3, 30, 255, cv2.THRESH_BINARY)[1]
        
        # 合并边缘检测结果
        edges_combined = cv2.bitwise_or(cv2.bitwise_or(edges1, edges2), edges3)
        
        # 膨胀操作连接文字
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (5, 5))
        edges_combined = cv2.dilate(edges_combined, kernel, iterations=2)
        
        # 查找轮廓并过滤
        contours, _ = cv2.findContours(edges_combined, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        mask = np.zeros_like(gray)
        for contour in contours:
            area = cv2.contourArea(contour)
            # 调整面积范围以更好地匹配文字
            if 20 < area < 3000:
                cv2.fillPoly(mask, [contour], 255)
        
        return mask
    
    def _detect_repetitive_pattern(self, gray):
        """检测重复模式"""
        # 使用模板匹配检测重复的文字模式
        height, width = gray.shape
        
        # 创建多个可能的水印模板
        templates = self._create_watermark_templates(gray)
        
        combined_mask = np.zeros_like(gray)
        
        for template in templates:
            if template is not None and template.size > 0:
                # 模板匹配
                result = cv2.matchTemplate(gray, template, cv2.TM_CCOEFF_NORMED)
                
                # 找到匹配位置
                threshold = 0.3  # 降低阈值以检测更多匹配
                locations = np.where(result >= threshold)
                
                # 在匹配位置创建遮罩
                th, tw = template.shape
                for pt in zip(*locations[::-1]):
                    cv2.rectangle(combined_mask, pt, (pt[0] + tw, pt[1] + th), 255, -1)
        
        return combined_mask
    
    def _create_watermark_templates(self, gray):
        """创建可能的水印模板"""
        templates = []
        
        # 从图像中提取可能的水印区域作为模板
        height, width = gray.shape
        
        # 在图像的不同位置提取小块作为模板
        template_positions = [
            (width//4, height//4, 150, 30),
            (width//2, height//3, 150, 30),
            (3*width//4, height//2, 150, 30),
        ]
        
        for x, y, w, h in template_positions:
            if x + w < width and y + h < height:
                template = gray[y:y+h, x:x+w]
                if template.size > 0:
                    templates.append(template)
        
        return templates
    
    def _detect_contrast_changes(self, gray):
        """检测对比度变化"""
        # 计算局部对比度
        kernel_size = 15
        kernel = np.ones((kernel_size, kernel_size), np.float32) / (kernel_size * kernel_size)
        
        # 局部均值
        local_mean = cv2.filter2D(gray.astype(np.float32), -1, kernel)
        
        # 局部方差
        local_var = cv2.filter2D((gray.astype(np.float32) - local_mean)**2, -1, kernel)
        
        # 对比度
        contrast = np.sqrt(local_var)
        
        # 检测对比度异常的区域
        contrast_normalized = cv2.normalize(contrast, None, 0, 255, cv2.NORM_MINMAX).astype(np.uint8)
        
        # 使用自适应阈值
        mask = cv2.adaptiveThreshold(contrast_normalized, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                   cv2.THRESH_BINARY, 11, 2)
        
        # 反转遮罩（我们要检测对比度较低的区域）
        mask = cv2.bitwise_not(mask)
        
        return mask
    
    def _smart_combine_masks(self, masks):
        """智能组合多个遮罩"""
        if not masks:
            return np.zeros((100, 100), dtype=np.uint8)
        
        # 加权组合
        weights = [0.3, 0.3, 0.25, 0.15]  # 调整权重
        
        combined = np.zeros_like(masks[0], dtype=np.float32)
        
        for i, mask in enumerate(masks):
            if i < len(weights):
                combined += mask.astype(np.float32) * weights[i]
        
        # 归一化并二值化
        combined = cv2.normalize(combined, None, 0, 255, cv2.NORM_MINMAX)
        _, combined = cv2.threshold(combined.astype(np.uint8), 100, 255, cv2.THRESH_BINARY)
        
        return combined
    
    def _post_process_mask(self, mask, gray):
        """后处理遮罩"""
        # 去除噪声
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel, iterations=1)
        
        # 连接断开的区域
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (7, 7))
        mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel, iterations=3)
        
        # 膨胀以确保完全覆盖水印
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
        mask = cv2.dilate(mask, kernel, iterations=2)
        
        return mask
    
    def remove_watermark_advanced(self, image, mask):
        """高级水印去除"""
        # 使用多种inpainting方法的组合
        
        # 方法1: Telea算法
        result1 = cv2.inpaint(image, mask, 5, cv2.INPAINT_TELEA)
        
        # 方法2: Navier-Stokes算法
        result2 = cv2.inpaint(image, mask, 5, cv2.INPAINT_NS)
        
        # 方法3: 基于周围像素的简单修复
        result3 = self._simple_inpaint(image, mask)
        
        # 智能融合三种结果
        final_result = self._blend_results([result1, result2, result3], mask)
        
        return final_result
    
    def _simple_inpaint(self, image, mask):
        """简单的基于周围像素的修复"""
        result = image.copy()
        
        # 找到需要修复的区域
        mask_coords = np.where(mask == 255)
        
        for y, x in zip(mask_coords[0], mask_coords[1]):
            # 获取周围非遮罩区域的像素
            neighbors = []
            for dy in range(-3, 4):
                for dx in range(-3, 4):
                    ny, nx = y + dy, x + dx
                    if (0 <= ny < image.shape[0] and 0 <= nx < image.shape[1] and 
                        mask[ny, nx] == 0):  # 非遮罩区域
                        neighbors.append(image[ny, nx])
            
            if neighbors:
                # 使用邻居像素的中值
                result[y, x] = np.median(neighbors, axis=0)
        
        return result
    
    def _blend_results(self, results, mask):
        """融合多个修复结果"""
        if len(results) == 0:
            return results[0]
        
        # 简单平均融合
        blended = np.zeros_like(results[0], dtype=np.float32)
        weights = [0.4, 0.4, 0.2]  # Telea, NS, Simple的权重
        
        for i, result in enumerate(results):
            if i < len(weights):
                blended += result.astype(np.float32) * weights[i]
        
        return blended.astype(np.uint8)
    
    def process_single_image(self, input_path, output_path, save_debug=False):
        """处理单张图片"""
        try:
            logger.info(f"开始处理: {input_path}")
            
            # 读取图片
            image = cv2.imread(str(input_path))
            if image is None:
                logger.error(f"无法读取图片: {input_path}")
                return False
            
            # 改进的水印检测
            logger.info("执行改进的水印检测...")
            mask = self.detect_watermark_improved(image)
            
            # 保存调试信息
            if save_debug:
                debug_dir = output_path.parent / "debug"
                debug_dir.mkdir(exist_ok=True)
                
                mask_path = debug_dir / f"{output_path.stem}_mask.png"
                cv2.imwrite(str(mask_path), mask)
                logger.info(f"遮罩已保存: {mask_path}")
            
            # 高级水印去除
            logger.info("执行高级水印去除...")
            result = self.remove_watermark_advanced(image, mask)
            
            # 保存结果
            cv2.imwrite(str(output_path), result)
            logger.info(f"处理完成: {output_path}")
            
            return True
            
        except Exception as e:
            logger.error(f"处理图片时出错 {input_path}: {str(e)}")
            return False
    
    def batch_process(self, input_dir, output_dir, save_debug=False):
        """批量处理图片"""
        input_path = Path(input_dir)
        output_path = Path(output_dir)
        
        # 创建输出目录
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 获取所有支持的图片文件
        image_files = []
        for ext in self.supported_formats:
            image_files.extend(input_path.glob(f"*{ext}"))
            image_files.extend(input_path.glob(f"*{ext.upper()}"))
        
        if not image_files:
            logger.warning(f"在 {input_dir} 中没有找到支持的图片文件")
            return
        
        logger.info(f"找到 {len(image_files)} 个图片文件")
        
        success_count = 0
        for i, img_file in enumerate(image_files, 1):
            logger.info(f"处理进度: {i}/{len(image_files)}")
            output_file = output_path / f"improved_{img_file.name}"
            
            if self.process_single_image(img_file, output_file, save_debug):
                success_count += 1
        
        logger.info(f"批量处理完成: {success_count}/{len(image_files)} 个文件处理成功")

def main():
    parser = argparse.ArgumentParser(description='改进的水印去除工具')
    parser.add_argument('--input', '-i', required=True, help='输入图片文件或目录')
    parser.add_argument('--output', '-o', required=True, help='输出文件或目录')
    parser.add_argument('--debug', action='store_true', help='保存调试信息')
    
    args = parser.parse_args()
    
    remover = ImprovedWatermarkRemover()
    
    input_path = Path(args.input)
    output_path = Path(args.output)
    
    logger.info("🚀 改进的水印去除工具启动")
    logger.info("=" * 50)
    
    if input_path.is_file():
        # 处理单个文件
        remover.process_single_image(input_path, output_path, args.debug)
    elif input_path.is_dir():
        # 批量处理
        remover.batch_process(input_path, output_path, args.debug)
    else:
        logger.error(f"输入路径不存在: {input_path}")

if __name__ == "__main__":
    main()
