#!/usr/bin/env python3
"""
智能水印去除工具
专门针对倾斜的 WWW.ABSKOOP.COM 水印
优化性能，精确检测
"""

import os
import sys
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

try:
    from PIL import Image, ImageFilter, ImageEnhance, ImageDraw
    import numpy as np
    from scipy import ndimage
    SCIPY_AVAILABLE = True
except ImportError:
    try:
        from PIL import Image, ImageFilter, ImageEnhance, ImageDraw
        import numpy as np
        SCIPY_AVAILABLE = False
        logger.warning("scipy不可用，使用简化算法")
    except ImportError:
        logger.error("需要安装 Pillow 和 numpy: pip install Pillow numpy")
        sys.exit(1)

class SmartWatermarkRemover:
    def __init__(self):
        self.target_text = "WWW.ABSKOOP.COM"
        
    def detect_watermark_smart(self, image_path):
        """
        智能检测水印 - 结合颜色和边缘特征
        """
        try:
            with Image.open(image_path) as img:
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # 缩小图片以提高处理速度
                scale_factor = 0.5
                small_img = img.resize((int(img.width * scale_factor), 
                                      int(img.height * scale_factor)), 
                                     Image.Resampling.LANCZOS)
                
                img_array = np.array(small_img)
                height, width = img_array.shape[:2]
                
                logger.info(f"处理尺寸: {width} x {height} (缩放因子: {scale_factor})")
                
                # 转换为灰度
                gray = np.dot(img_array[...,:3], [0.2989, 0.5870, 0.1140])
                
                # 检测水印区域
                regions = self._detect_watermark_regions(gray)
                
                # 将坐标缩放回原图尺寸
                scaled_regions = []
                for region in regions:
                    x1, y1, x2, y2 = region
                    scaled_region = (
                        int(x1 / scale_factor),
                        int(y1 / scale_factor),
                        int(x2 / scale_factor),
                        int(y2 / scale_factor)
                    )
                    scaled_regions.append(scaled_region)
                
                logger.info(f"检测到 {len(scaled_regions)} 个水印区域")
                return scaled_regions
                
        except Exception as e:
            logger.error(f"检测失败 {image_path}: {e}")
            return []
    
    def _detect_watermark_regions(self, gray):
        """
        检测水印区域 - 基于颜色和纹理特征
        """
        height, width = gray.shape
        regions = []
        
        # 水印特征参数
        watermark_gray_min = 160
        watermark_gray_max = 210
        
        # 创建水印候选掩码
        watermark_mask = (gray >= watermark_gray_min) & (gray <= watermark_gray_max)
        
        # 计算边缘强度
        if SCIPY_AVAILABLE:
            # 使用Sobel算子检测边缘
            sobel_x = ndimage.sobel(gray, axis=1)
            sobel_y = ndimage.sobel(gray, axis=0)
            edge_magnitude = np.sqrt(sobel_x**2 + sobel_y**2)
        else:
            # 简单梯度计算
            grad_x = np.gradient(gray, axis=1)
            grad_y = np.gradient(gray, axis=0)
            edge_magnitude = np.sqrt(grad_x**2 + grad_y**2)
        
        # 边缘掩码
        edge_mask = edge_magnitude > 8
        
        # 结合颜色和边缘特征
        combined_mask = watermark_mask & edge_mask
        
        # 使用滑动窗口检测
        window_size = 60
        step = 30
        
        for y in range(0, height - window_size, step):
            for x in range(0, width - window_size, step):
                window_mask = combined_mask[y:y+window_size, x:x+window_size]
                
                # 计算窗口内的特征
                mask_density = np.sum(window_mask) / (window_size * window_size)
                
                # 检查是否符合水印特征
                if mask_density > 0.05:  # 至少5%的像素符合条件
                    # 进一步验证
                    window_gray = gray[y:y+window_size, x:x+window_size]
                    
                    if self._validate_watermark_window(window_gray, window_mask):
                        regions.append((x, y, x + window_size, y + window_size))
        
        # 合并相邻区域
        merged_regions = self._merge_adjacent_regions(regions)
        
        return merged_regions
    
    def _validate_watermark_window(self, window_gray, window_mask):
        """
        验证窗口是否包含水印
        """
        if np.sum(window_mask) < 10:
            return False
        
        # 提取掩码区域的像素值
        masked_pixels = window_gray[window_mask]
        
        if len(masked_pixels) == 0:
            return False
        
        mean_val = np.mean(masked_pixels)
        std_val = np.std(masked_pixels)
        
        # 水印特征验证
        return (170 < mean_val < 200 and  # 特定灰度范围
                5 < std_val < 20)         # 适度的对比度变化
    
    def _merge_adjacent_regions(self, regions):
        """
        合并相邻的区域
        """
        if not regions:
            return []
        
        merged = []
        regions = sorted(regions, key=lambda r: (r[1], r[0]))  # 按y,x排序
        
        for region in regions:
            x1, y1, x2, y2 = region
            
            # 检查是否可以与现有区域合并
            merged_with_existing = False
            for i, existing in enumerate(merged):
                ex1, ey1, ex2, ey2 = existing
                
                # 检查是否相邻或重叠
                if (abs(x1 - ex2) < 20 or abs(ex1 - x2) < 20 or  # 水平相邻
                    abs(y1 - ey2) < 20 or abs(ey1 - y2) < 20):   # 垂直相邻
                    
                    # 合并区域
                    new_region = (min(x1, ex1), min(y1, ey1), 
                                max(x2, ex2), max(y2, ey2))
                    merged[i] = new_region
                    merged_with_existing = True
                    break
            
            if not merged_with_existing:
                merged.append(region)
        
        return merged
    
    def remove_watermark_gentle(self, image_path, output_path, regions):
        """
        温和地去除水印
        """
        try:
            with Image.open(image_path) as img:
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                logger.info(f"开始处理 {len(regions)} 个水印区域")
                
                for i, region in enumerate(regions):
                    x1, y1, x2, y2 = region
                    
                    # 确保坐标有效
                    x1 = max(0, x1)
                    y1 = max(0, y1)
                    x2 = min(img.width, x2)
                    y2 = min(img.height, y2)
                    
                    if x2 <= x1 or y2 <= y1:
                        continue
                    
                    # 扩展边界以确保完全覆盖
                    margin = 5
                    x1 = max(0, x1 - margin)
                    y1 = max(0, y1 - margin)
                    x2 = min(img.width, x2 + margin)
                    y2 = min(img.height, y2 + margin)
                    
                    # 提取区域
                    roi = img.crop((x1, y1, x2, y2))
                    
                    # 温和处理
                    # 1. 轻微模糊
                    blurred = roi.filter(ImageFilter.GaussianBlur(radius=1.2))
                    
                    # 2. 提亮
                    enhancer = ImageEnhance.Brightness(blurred)
                    brightened = enhancer.enhance(1.1)
                    
                    # 3. 降低对比度
                    contrast_enhancer = ImageEnhance.Contrast(brightened)
                    final_roi = contrast_enhancer.enhance(0.9)
                    
                    # 粘贴回去
                    img.paste(final_roi, (x1, y1))
                    
                    logger.info(f"处理区域 {i+1}: ({x1}, {y1}, {x2}, {y2})")
                
                # 保存结果
                img.save(output_path, quality=95)
                return True
                
        except Exception as e:
            logger.error(f"修复失败 {image_path}: {e}")
            return False
    
    def create_debug_image(self, image_path, regions, debug_path):
        """
        创建调试图像，只标记水印区域
        """
        try:
            with Image.open(image_path) as img:
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                draw = ImageDraw.Draw(img)
                
                for i, region in enumerate(regions):
                    x1, y1, x2, y2 = region
                    # 绘制红色边框
                    draw.rectangle([x1, y1, x2, y2], outline='red', width=4)
                    # 添加标签
                    draw.text((x1, y1-25), f"WATERMARK-{i+1}", fill='red')
                
                img.save(debug_path, quality=95)
                logger.info(f"调试图像已保存: {debug_path}")
                
        except Exception as e:
            logger.error(f"创建调试图像失败: {e}")
    
    def process_single_image(self, input_path, output_path, create_debug=True):
        """
        处理单张图片
        """
        try:
            logger.info(f"处理: {input_path}")
            
            # 智能检测水印
            regions = self.detect_watermark_smart(input_path)
            
            if not regions:
                logger.info(f"未检测到水印，复制原图")
                import shutil
                shutil.copy2(input_path, output_path)
                return True
            
            # 创建调试图像
            if create_debug:
                debug_path = str(output_path).replace('.jpg', '_debug.jpg')
                self.create_debug_image(input_path, regions, debug_path)
            
            # 去除水印
            success = self.remove_watermark_gentle(input_path, output_path, regions)
            
            if success:
                logger.info(f"处理完成: {output_path}")
            
            return success
            
        except Exception as e:
            logger.error(f"处理失败 {input_path}: {e}")
            return False

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='智能水印去除工具')
    parser.add_argument('--input', '-i', required=True, help='输入目录或文件')
    parser.add_argument('--output', '-o', required=True, help='输出目录或文件')
    parser.add_argument('--debug', action='store_true', help='创建调试图像')
    
    args = parser.parse_args()
    
    remover = SmartWatermarkRemover()
    
    logger.info("🧠 智能水印去除工具启动")
    logger.info("=" * 50)
    
    input_path = Path(args.input)
    output_path = Path(args.output)
    
    if input_path.is_file():
        output_path.parent.mkdir(parents=True, exist_ok=True)
        remover.process_single_image(input_path, output_path, args.debug)
    else:
        output_path.mkdir(parents=True, exist_ok=True)
        
        supported_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
        image_files = []
        for ext in supported_formats:
            image_files.extend(input_path.glob(f"*{ext}"))
            image_files.extend(input_path.glob(f"*{ext.upper()}"))
        
        if not image_files:
            logger.error(f"在 {args.input} 中未找到支持的图片文件")
            return
        
        logger.info(f"找到 {len(image_files)} 个图片文件")
        
        for img_file in image_files:
            output_file = output_path / img_file.name
            remover.process_single_image(img_file, output_file, args.debug)

if __name__ == "__main__":
    main()
