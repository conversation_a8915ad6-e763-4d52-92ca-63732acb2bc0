#!/usr/bin/env python3
"""
智能水印去除工具 - 最终优化版本
结合多种检测方法和智能决策
"""

import cv2
import numpy as np
import os
import argparse
from pathlib import Path
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SmartWatermarkRemover:
    def __init__(self):
        self.supported_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
    
    def analyze_image_characteristics(self, image):
        """分析图像特征以优化检测参数"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 计算图像统计信息
        mean_brightness = np.mean(gray)
        std_brightness = np.std(gray)
        
        # 计算对比度
        contrast = std_brightness / mean_brightness if mean_brightness > 0 else 0
        
        logger.info(f"图像分析 - 平均亮度: {mean_brightness:.1f}, 标准差: {std_brightness:.1f}, 对比度: {contrast:.3f}")
        
        return {
            'mean_brightness': mean_brightness,
            'std_brightness': std_brightness,
            'contrast': contrast
        }
    
    def detect_watermark_smart(self, image):
        """
        智能水印检测 - 根据图像特征自适应调整参数
        """
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        height, width = gray.shape
        
        # 分析图像特征
        img_stats = self.analyze_image_characteristics(image)
        
        logger.info("开始智能水印检测...")
        
        # 根据图像特征调整检测参数
        if img_stats['mean_brightness'] > 150:
            # 亮图像 - 水印可能更明显
            brightness_range = (180, 220)
            canny_low, canny_high = 25, 75
        else:
            # 暗图像 - 需要更敏感的检测
            brightness_range = (160, 200)
            canny_low, canny_high = 20, 60
        
        # 方法1: 自适应亮度检测
        mask1 = cv2.inRange(gray, brightness_range[0], brightness_range[1])
        
        # 方法2: 自适应边缘检测
        edges = cv2.Canny(gray, canny_low, canny_high)
        
        # 方法3: 基于局部对比度的检测
        mask3 = self._detect_by_local_contrast(gray)
        
        # 方法4: 文字特征检测
        mask4 = self._detect_text_regions(gray)
        
        # 智能组合策略
        final_mask = self._intelligent_combine(
            [mask1, edges, mask3, mask4], 
            ['brightness', 'edges', 'contrast', 'text'],
            img_stats
        )
        
        # 后处理
        final_mask = self._smart_post_process(final_mask, gray)
        
        logger.info("智能水印检测完成")
        return final_mask
    
    def _detect_by_local_contrast(self, gray):
        """基于局部对比度检测水印"""
        # 计算局部标准差
        kernel = np.ones((15, 15), np.float32) / 225
        local_mean = cv2.filter2D(gray.astype(np.float32), -1, kernel)
        local_sq_mean = cv2.filter2D((gray.astype(np.float32))**2, -1, kernel)
        local_var = local_sq_mean - local_mean**2
        local_std = np.sqrt(np.maximum(local_var, 0))
        
        # 检测对比度异常的区域
        std_normalized = cv2.normalize(local_std, None, 0, 255, cv2.NORM_MINMAX).astype(np.uint8)
        
        # 使用阈值检测低对比度区域（可能是水印）
        _, mask = cv2.threshold(std_normalized, 30, 255, cv2.THRESH_BINARY_INV)
        
        return mask
    
    def _detect_text_regions(self, gray):
        """检测文字区域"""
        # 使用多种方法检测文字
        
        # 方法1: 基于梯度的文字检测
        sobelx = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
        sobely = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
        gradient_mag = np.sqrt(sobelx**2 + sobely**2)
        gradient_mag = cv2.normalize(gradient_mag, None, 0, 255, cv2.NORM_MINMAX).astype(np.uint8)
        
        # 方法2: 形态学文字检测
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 1))
        morph = cv2.morphologyEx(gradient_mag, cv2.MORPH_CLOSE, kernel)
        
        # 阈值化
        _, text_mask = cv2.threshold(morph, 50, 255, cv2.THRESH_BINARY)
        
        # 连接文字组件
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (5, 3))
        text_mask = cv2.morphologyEx(text_mask, cv2.MORPH_CLOSE, kernel)
        
        return text_mask
    
    def _intelligent_combine(self, masks, mask_types, img_stats):
        """智能组合多个检测结果"""
        if not masks:
            return np.zeros((100, 100), dtype=np.uint8)
        
        # 根据图像特征调整权重
        if img_stats['contrast'] > 0.3:
            # 高对比度图像 - 更依赖边缘检测
            weights = [0.3, 0.4, 0.2, 0.1]  # brightness, edges, contrast, text
        else:
            # 低对比度图像 - 更依赖亮度检测
            weights = [0.5, 0.2, 0.2, 0.1]
        
        # 加权组合
        combined = np.zeros_like(masks[0], dtype=np.float32)
        for i, mask in enumerate(masks):
            if i < len(weights):
                combined += mask.astype(np.float32) * weights[i]
        
        # 自适应阈值
        threshold = 80 if img_stats['contrast'] > 0.3 else 100
        
        # 归一化并二值化
        combined = cv2.normalize(combined, None, 0, 255, cv2.NORM_MINMAX)
        _, combined = cv2.threshold(combined.astype(np.uint8), threshold, 255, cv2.THRESH_BINARY)
        
        return combined
    
    def _smart_post_process(self, mask, gray):
        """智能后处理"""
        # 去除小噪声
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel, iterations=1)
        
        # 连接断开的区域
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
        mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel, iterations=2)
        
        # 检查遮罩质量
        mask_area = np.sum(mask == 255)
        total_area = mask.shape[0] * mask.shape[1]
        mask_ratio = mask_area / total_area
        
        # 如果遮罩过大，进行额外过滤
        if mask_ratio > 0.3:
            logger.warning(f"遮罩过大 ({mask_ratio:.2%})，进行额外过滤")
            # 使用更严格的形态学操作
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (7, 7))
            mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel, iterations=2)
        
        # 轻微膨胀以确保覆盖水印边缘
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (2, 2))
        mask = cv2.dilate(mask, kernel, iterations=1)
        
        return mask
    
    def remove_watermark_optimized(self, image, mask):
        """优化的水印去除"""
        logger.info("开始优化水印去除...")
        
        # 检查遮罩大小决定使用的方法
        mask_area = np.sum(mask == 255)
        total_area = mask.shape[0] * mask.shape[1]
        mask_ratio = mask_area / total_area
        
        if mask_ratio < 0.05:
            # 小面积水印 - 使用精细修复
            inpaint_radius = 3
            result = cv2.inpaint(image, mask, inpaint_radius, cv2.INPAINT_TELEA)
        elif mask_ratio < 0.2:
            # 中等面积水印 - 使用标准修复
            inpaint_radius = 5
            result = cv2.inpaint(image, mask, inpaint_radius, cv2.INPAINT_TELEA)
        else:
            # 大面积水印 - 使用更大的修复半径
            inpaint_radius = 7
            result = cv2.inpaint(image, mask, inpaint_radius, cv2.INPAINT_NS)
        
        # 后处理增强
        result = self._enhance_result(result, image, mask)
        
        logger.info("优化水印去除完成")
        return result
    
    def _enhance_result(self, result, original, mask):
        """增强处理结果"""
        # 在非遮罩区域保持原始图像
        mask_inv = cv2.bitwise_not(mask)
        
        # 创建混合权重
        mask_blur = cv2.GaussianBlur(mask.astype(np.float32), (15, 15), 0) / 255.0
        mask_blur = np.stack([mask_blur] * 3, axis=2)
        
        # 平滑混合
        enhanced = result.astype(np.float32) * mask_blur + original.astype(np.float32) * (1 - mask_blur)
        
        return enhanced.astype(np.uint8)
    
    def process_single_image(self, input_path, output_path, save_debug=False):
        """处理单张图片"""
        try:
            logger.info(f"开始处理: {input_path}")
            
            # 读取图片
            image = cv2.imread(str(input_path))
            if image is None:
                logger.error(f"无法读取图片: {input_path}")
                return False
            
            logger.info(f"图片尺寸: {image.shape}")
            
            # 智能检测水印
            mask = self.detect_watermark_smart(image)
            
            # 检查遮罩质量
            mask_area = np.sum(mask == 255)
            total_area = mask.shape[0] * mask.shape[1]
            mask_ratio = mask_area / total_area
            
            logger.info(f"检测到的水印区域占比: {mask_ratio:.2%}")
            
            # 保存调试信息
            if save_debug:
                debug_dir = output_path.parent / "debug"
                debug_dir.mkdir(exist_ok=True)
                
                mask_path = debug_dir / f"{output_path.stem}_mask.png"
                cv2.imwrite(str(mask_path), mask)
                logger.info(f"遮罩已保存: {mask_path}")
            
            # 优化去除水印
            result = self.remove_watermark_optimized(image, mask)
            
            # 保存结果
            cv2.imwrite(str(output_path), result)
            logger.info(f"处理完成: {output_path}")
            
            return True
            
        except Exception as e:
            logger.error(f"处理图片时出错 {input_path}: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
    
    def batch_process(self, input_dir, output_dir, save_debug=False):
        """批量处理图片"""
        input_path = Path(input_dir)
        output_path = Path(output_dir)
        
        # 创建输出目录
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 获取所有支持的图片文件
        image_files = []
        for ext in self.supported_formats:
            image_files.extend(input_path.glob(f"*{ext}"))
            image_files.extend(input_path.glob(f"*{ext.upper()}"))
        
        if not image_files:
            logger.warning(f"在 {input_dir} 中没有找到支持的图片文件")
            return
        
        logger.info(f"找到 {len(image_files)} 个图片文件")
        
        success_count = 0
        for i, img_file in enumerate(image_files, 1):
            logger.info(f"处理进度: {i}/{len(image_files)}")
            output_file = output_path / f"smart_{img_file.name}"
            
            if self.process_single_image(img_file, output_file, save_debug):
                success_count += 1
        
        logger.info(f"批量处理完成: {success_count}/{len(image_files)} 个文件处理成功")

def main():
    parser = argparse.ArgumentParser(description='智能水印去除工具')
    parser.add_argument('--input', '-i', required=True, help='输入图片文件或目录')
    parser.add_argument('--output', '-o', required=True, help='输出文件或目录')
    parser.add_argument('--debug', action='store_true', help='保存调试信息')
    
    args = parser.parse_args()
    
    remover = SmartWatermarkRemover()
    
    input_path = Path(args.input)
    output_path = Path(args.output)
    
    logger.info("🧠 智能水印去除工具启动")
    logger.info("=" * 50)
    
    if input_path.is_file():
        # 处理单个文件
        remover.process_single_image(input_path, output_path, args.debug)
    elif input_path.is_dir():
        # 批量处理
        remover.batch_process(input_path, output_path, args.debug)
    else:
        logger.error(f"输入路径不存在: {input_path}")

if __name__ == "__main__":
    main()
