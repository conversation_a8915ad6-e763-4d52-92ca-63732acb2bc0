#!/usr/bin/env python3
"""
针对性水印去除工具
基于手动识别的水印位置进行精确处理
"""

import os
import sys
from pathlib import Path
import logging
import numpy as np
from PIL import Image, ImageFilter, ImageEnhance

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TargetedWatermarkRemover:
    def __init__(self, grid_size=200):
        self.grid_size = grid_size
        
    def get_grid_coordinates(self, grid_id, image_width, image_height):
        """
        根据网格ID计算实际坐标
        """
        # 计算每行的网格数量
        grids_per_row = (image_width + self.grid_size - 1) // self.grid_size
        
        # 计算行和列
        row = (grid_id - 1) // grids_per_row
        col = (grid_id - 1) % grids_per_row
        
        # 计算坐标
        x = col * self.grid_size
        y = row * self.grid_size
        
        # 确保不超出图片边界
        x2 = min(x + self.grid_size, image_width)
        y2 = min(y + self.grid_size, image_height)
        
        return (x, y, x2, y2)
    
    def remove_watermark_from_region(self, image, region_coords):
        """
        从指定区域去除水印
        """
        x1, y1, x2, y2 = region_coords
        
        # 提取区域
        region = image.crop((x1, y1, x2, y2))
        region_array = np.array(region)
        
        logger.info(f"处理区域 ({x1}, {y1}, {x2}, {y2}) 尺寸: {x2-x1}x{y2-y1}")
        
        # 方法1: 轻微模糊 + 亮度调整
        processed_region = self.method_blur_brighten(region)
        
        # 方法2: 如果效果不好，尝试更强的处理
        if self.needs_stronger_processing(region_array):
            processed_region = self.method_strong_processing(region)
        
        # 粘贴回原图
        image.paste(processed_region, (x1, y1))
        
        return image
    
    def method_blur_brighten(self, region):
        """
        方法1: 轻微模糊 + 亮度调整
        """
        # 轻微高斯模糊
        blurred = region.filter(ImageFilter.GaussianBlur(radius=1.0))
        
        # 提高亮度，让水印接近背景
        enhancer = ImageEnhance.Brightness(blurred)
        brightened = enhancer.enhance(1.08)  # 提高8%亮度
        
        # 轻微降低对比度
        contrast_enhancer = ImageEnhance.Contrast(brightened)
        final_region = contrast_enhancer.enhance(0.95)  # 降低5%对比度
        
        return final_region
    
    def method_strong_processing(self, region):
        """
        方法2: 更强的处理（用于顽固水印）
        """
        # 更强的模糊
        blurred = region.filter(ImageFilter.GaussianBlur(radius=1.5))
        
        # 更大的亮度调整
        enhancer = ImageEnhance.Brightness(blurred)
        brightened = enhancer.enhance(1.12)  # 提高12%亮度
        
        # 降低对比度
        contrast_enhancer = ImageEnhance.Contrast(brightened)
        processed = contrast_enhancer.enhance(0.90)  # 降低10%对比度
        
        # 轻微的色彩饱和度调整
        color_enhancer = ImageEnhance.Color(processed)
        final_region = color_enhancer.enhance(0.98)  # 轻微降低饱和度
        
        return final_region
    
    def needs_stronger_processing(self, region_array):
        """
        判断是否需要更强的处理
        """
        # 转换为灰度
        gray = np.dot(region_array[...,:3], [0.2989, 0.5870, 0.1140])
        
        # 计算标准差，如果对比度很高，需要更强处理
        std_val = np.std(gray)
        mean_val = np.mean(gray)
        
        # 如果标准差高且平均亮度适中，可能是明显的水印
        return std_val > 15 and 200 < mean_val < 250
    
    def process_image_with_grid_ids(self, input_path, output_path, grid_ids):
        """
        根据网格ID处理图片
        """
        try:
            with Image.open(input_path) as img:
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                width, height = img.size
                logger.info(f"处理图片: {input_path} ({width}x{height})")
                logger.info(f"目标网格: {grid_ids}")
                
                # 处理每个指定的网格
                for grid_id in grid_ids:
                    coords = self.get_grid_coordinates(grid_id, width, height)
                    img = self.remove_watermark_from_region(img, coords)
                
                # 保存结果
                img.save(output_path, quality=98)
                logger.info(f"处理完成: {output_path}")
                
                return True
                
        except Exception as e:
            logger.error(f"处理失败 {input_path}: {e}")
            return False
    
    def create_comparison_image(self, original_path, processed_path, output_path, grid_ids):
        """
        创建对比图像
        """
        try:
            with Image.open(original_path) as original, Image.open(processed_path) as processed:
                # 创建并排对比图
                width, height = original.size
                comparison = Image.new('RGB', (width * 2, height), 'white')
                
                # 粘贴原图和处理后的图
                comparison.paste(original, (0, 0))
                comparison.paste(processed, (width, 0))
                
                # 在处理后的图上标记处理的区域
                from PIL import ImageDraw, ImageFont
                draw = ImageDraw.Draw(comparison)
                
                try:
                    font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 16)
                except:
                    font = ImageFont.load_default()
                
                # 标记处理的网格
                for grid_id in grid_ids:
                    coords = self.get_grid_coordinates(grid_id, width, height)
                    x1, y1, x2, y2 = coords
                    
                    # 在右侧图上绘制边框
                    draw.rectangle([width + x1, y1, width + x2, y2], outline='red', width=2)
                    draw.text((width + x1 + 5, y1 + 5), f"Grid {grid_id}", fill='red', font=font)
                
                # 添加标题
                draw.text((10, 10), "原图", fill='black', font=font)
                draw.text((width + 10, 10), "处理后", fill='black', font=font)
                
                comparison.save(output_path, quality=95)
                logger.info(f"对比图已保存: {output_path}")
                
        except Exception as e:
            logger.error(f"创建对比图失败: {e}")

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='针对性水印去除工具')
    parser.add_argument('--input', '-i', required=True, help='输入图片路径')
    parser.add_argument('--output', '-o', required=True, help='输出图片路径')
    parser.add_argument('--grids', '-g', required=True, help='网格ID列表，用逗号分隔，如: 31,32,73')
    parser.add_argument('--comparison', '-c', help='对比图输出路径（可选）')
    
    args = parser.parse_args()
    
    # 解析网格ID
    try:
        grid_ids = [int(x.strip()) for x in args.grids.split(',')]
    except ValueError:
        logger.error("网格ID格式错误，请使用逗号分隔的数字，如: 31,32,73")
        sys.exit(1)
    
    remover = TargetedWatermarkRemover()
    
    logger.info("🎯 针对性水印去除工具启动")
    logger.info("=" * 50)
    
    # 处理图片
    success = remover.process_image_with_grid_ids(args.input, args.output, grid_ids)
    
    if success and args.comparison:
        # 创建对比图
        remover.create_comparison_image(args.input, args.output, args.comparison, grid_ids)
    
    if success:
        logger.info("✅ 处理完成！")
    else:
        logger.error("❌ 处理失败！")
        sys.exit(1)

if __name__ == "__main__":
    main()
