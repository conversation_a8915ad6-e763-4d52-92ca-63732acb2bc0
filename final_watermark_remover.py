#!/usr/bin/env python3
"""
最终版水印去除工具
基于你的反馈，重新设计算法
专门针对 WWW.ABSKOOP.COM 水印
"""

import os
import sys
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

try:
    from PIL import Image, ImageFilter, ImageEnhance, ImageDraw
    import numpy as np
except ImportError:
    logger.error("需要安装 Pillow 和 numpy: pip install Pillow numpy")
    sys.exit(1)

class FinalWatermarkRemover:
    def __init__(self):
        self.processed_count = 0
        
    def detect_watermark_by_color_pattern(self, image_path):
        """
        基于颜色模式检测水印
        专门针对灰色水印文字
        """
        try:
            with Image.open(image_path) as img:
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                img_array = np.array(img)
                height, width = img_array.shape[:2]
                
                logger.info(f"图片尺寸: {width} x {height}")
                
                # 转换为灰度
                gray = np.dot(img_array[...,:3], [0.2989, 0.5870, 0.1140])
                
                regions = []
                
                # 基于你的截图，水印是灰色文字
                # 我们寻找特定灰度范围的连续区域
                
                # 水印检测参数 - 基于分析结果
                watermark_gray_min = 160  # 水印的最小灰度
                watermark_gray_max = 200  # 水印的最大灰度
                
                # 创建水印掩码
                watermark_mask = (gray >= watermark_gray_min) & (gray <= watermark_gray_max)
                
                # 使用形态学操作连接相近的像素
                watermark_mask = self._morphological_operations(watermark_mask)
                
                # 查找连通区域
                regions = self._find_connected_regions(watermark_mask)
                
                # 过滤区域 - 只保留可能是文字的区域
                filtered_regions = []
                for region in regions:
                    x1, y1, x2, y2 = region
                    width_r = x2 - x1
                    height_r = y2 - y1
                    area = width_r * height_r
                    
                    # 文字区域的特征
                    if (20 < width_r < 300 and    # 宽度合理
                        10 < height_r < 100 and   # 高度合理
                        200 < area < 10000 and    # 面积合理
                        width_r > height_r):      # 文字通常比较宽
                        
                        filtered_regions.append(region)
                
                logger.info(f"检测到 {len(filtered_regions)} 个潜在水印区域")
                return filtered_regions
                
        except Exception as e:
            logger.error(f"检测失败 {image_path}: {e}")
            return []
    
    def _morphological_operations(self, mask):
        """
        形态学操作来连接相近的像素
        """
        # 简单的膨胀操作
        kernel_size = 3
        dilated = np.zeros_like(mask)
        
        for y in range(kernel_size//2, mask.shape[0] - kernel_size//2):
            for x in range(kernel_size//2, mask.shape[1] - kernel_size//2):
                if np.any(mask[y-kernel_size//2:y+kernel_size//2+1, 
                              x-kernel_size//2:x+kernel_size//2+1]):
                    dilated[y, x] = True
        
        return dilated
    
    def _find_connected_regions(self, mask):
        """
        查找连通区域
        """
        visited = np.zeros_like(mask, dtype=bool)
        regions = []
        
        def flood_fill(start_y, start_x):
            """洪水填充算法"""
            stack = [(start_y, start_x)]
            min_x, max_x = start_x, start_x
            min_y, max_y = start_y, start_y
            
            while stack:
                y, x = stack.pop()
                
                if (y < 0 or y >= mask.shape[0] or 
                    x < 0 or x >= mask.shape[1] or 
                    visited[y, x] or not mask[y, x]):
                    continue
                
                visited[y, x] = True
                min_x, max_x = min(min_x, x), max(max_x, x)
                min_y, max_y = min(min_y, y), max(max_y, y)
                
                # 添加相邻像素
                for dy, dx in [(-1,0), (1,0), (0,-1), (0,1)]:
                    stack.append((y+dy, x+dx))
            
            return (min_x, min_y, max_x+1, max_y+1)
        
        # 扫描所有像素
        for y in range(mask.shape[0]):
            for x in range(mask.shape[1]):
                if mask[y, x] and not visited[y, x]:
                    region = flood_fill(y, x)
                    if region:
                        regions.append(region)
        
        return regions
    
    def remove_watermark_gentle(self, image_path, output_path, regions):
        """
        温和地去除水印
        """
        try:
            with Image.open(image_path) as img:
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                logger.info(f"开始温和处理 {len(regions)} 个水印区域")
                
                for i, region in enumerate(regions):
                    x1, y1, x2, y2 = region
                    
                    # 扩展区域边界
                    margin = 5
                    x1 = max(0, x1 - margin)
                    y1 = max(0, y1 - margin)
                    x2 = min(img.width, x2 + margin)
                    y2 = min(img.height, y2 + margin)
                    
                    # 提取区域
                    roi = img.crop((x1, y1, x2, y2))
                    
                    # 温和处理
                    # 1. 轻微模糊
                    blurred = roi.filter(ImageFilter.GaussianBlur(radius=1.0))
                    
                    # 2. 轻微提亮（让水印接近背景色）
                    enhancer = ImageEnhance.Brightness(blurred)
                    brightened = enhancer.enhance(1.1)
                    
                    # 3. 轻微降低对比度
                    contrast_enhancer = ImageEnhance.Contrast(brightened)
                    final_roi = contrast_enhancer.enhance(0.9)
                    
                    # 粘贴回去
                    img.paste(final_roi, (x1, y1))
                    
                    if (i + 1) % 50 == 0:
                        logger.info(f"已处理 {i + 1}/{len(regions)} 个区域")
                
                # 保存高质量结果
                img.save(output_path, quality=95)
                return True
                
        except Exception as e:
            logger.error(f"修复失败 {image_path}: {e}")
            return False
    
    def create_debug_image(self, image_path, regions, debug_path):
        """
        创建调试图像，显示检测到的区域
        """
        try:
            with Image.open(image_path) as img:
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                draw = ImageDraw.Draw(img)
                
                for i, region in enumerate(regions):
                    x1, y1, x2, y2 = region
                    # 绘制红色边框
                    draw.rectangle([x1, y1, x2, y2], outline='red', width=2)
                    # 添加编号
                    draw.text((x1, y1-15), str(i+1), fill='red')
                
                img.save(debug_path, quality=95)
                logger.info(f"调试图像已保存: {debug_path}")
                
        except Exception as e:
            logger.error(f"创建调试图像失败: {e}")
    
    def process_single_image(self, input_path, output_path, create_debug=True):
        """
        处理单张图片
        """
        try:
            logger.info(f"处理: {input_path}")
            
            # 检测水印区域
            regions = self.detect_watermark_by_color_pattern(input_path)
            
            if not regions:
                logger.info(f"未检测到水印，复制原图: {input_path}")
                import shutil
                shutil.copy2(input_path, output_path)
                return True
            
            # 创建调试图像
            if create_debug:
                debug_path = str(output_path).replace('.jpg', '_debug.jpg')
                self.create_debug_image(input_path, regions, debug_path)
            
            # 温和去除水印
            success = self.remove_watermark_gentle(input_path, output_path, regions)
            
            if success:
                logger.info(f"处理完成: {output_path}")
            
            return success
            
        except Exception as e:
            logger.error(f"处理失败 {input_path}: {e}")
            return False

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='最终版水印去除工具')
    parser.add_argument('--input', '-i', required=True, help='输入目录或文件')
    parser.add_argument('--output', '-o', required=True, help='输出目录或文件')
    parser.add_argument('--debug', action='store_true', help='创建调试图像')
    
    args = parser.parse_args()
    
    remover = FinalWatermarkRemover()
    
    logger.info("🎯 最终版水印去除工具启动")
    logger.info("=" * 50)
    
    input_path = Path(args.input)
    output_path = Path(args.output)
    
    if input_path.is_file():
        output_path.parent.mkdir(parents=True, exist_ok=True)
        remover.process_single_image(input_path, output_path, args.debug)
    else:
        output_path.mkdir(parents=True, exist_ok=True)
        
        supported_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
        image_files = []
        for ext in supported_formats:
            image_files.extend(input_path.glob(f"*{ext}"))
            image_files.extend(input_path.glob(f"*{ext.upper()}"))
        
        if not image_files:
            logger.error(f"在 {args.input} 中未找到支持的图片文件")
            return
        
        logger.info(f"找到 {len(image_files)} 个图片文件")
        
        for img_file in image_files:
            output_file = output_path / img_file.name
            remover.process_single_image(img_file, output_file, args.debug)

if __name__ == "__main__":
    main()
