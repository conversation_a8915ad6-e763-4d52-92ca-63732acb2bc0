#!/usr/bin/env python3
"""
简化版水印去除工具
专门针对 WWW.ABSKOOP.COM 类型的半透明水印
基于你提供的截图优化参数
"""

import os
import sys
from pathlib import Path
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

try:
    from PIL import Image, ImageFilter, ImageEnhance
    import numpy as np
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    logger.error("需要安装 Pillow 和 numpy: pip install Pillow numpy")
    sys.exit(1)

class SimpleWatermarkRemover:
    def __init__(self):
        self.processed_count = 0
        
    def detect_watermark_regions(self, image_path):
        """
        基于你截图的水印特征进行检测
        """
        try:
            with Image.open(image_path) as img:
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # 转换为numpy数组
                img_array = np.array(img)
                height, width = img_array.shape[:2]
                
                # 转换为灰度
                gray = np.dot(img_array[...,:3], [0.2989, 0.5870, 0.1140])
                
                regions = []
                
                # 基于分析结果调整参数
                # 这是一个亮背景图片，水印在相对较深的灰度范围
                watermark_min = 170
                watermark_max = 235

                # 使用更小的块和更密集的扫描
                block_size = 60
                step = 30

                # 计算梯度来检测边缘
                grad_x = np.gradient(gray, axis=1)
                grad_y = np.gradient(gray, axis=0)
                gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2)

                for y in range(0, height - block_size, step):
                    for x in range(0, width - block_size, step):
                        block = gray[y:y+block_size, x:x+block_size]
                        block_grad = gradient_magnitude[y:y+block_size, x:x+block_size]

                        mean_brightness = np.mean(block)
                        std_brightness = np.std(block)
                        edge_density = np.sum(block_grad > 10) / block_grad.size

                        # 检测水印特征
                        if (watermark_min < mean_brightness < watermark_max and
                            3 < std_brightness < 30 and
                            edge_density > 0.02):  # 有足够的边缘特征

                            regions.append((x, y, x + block_size, y + block_size))
                
                # 合并重叠区域
                merged_regions = self._merge_overlapping_regions(regions)
                
                logger.info(f"检测到 {len(merged_regions)} 个潜在水印区域")
                return merged_regions
                
        except Exception as e:
            logger.error(f"检测失败 {image_path}: {e}")
            return []
    
    def _merge_overlapping_regions(self, regions):
        """
        合并重叠的区域
        """
        if not regions:
            return []
        
        merged = []
        used = set()
        
        for i, region1 in enumerate(regions):
            if i in used:
                continue
            
            x1, y1, x2, y2 = region1
            
            # 查找重叠区域并合并
            for j in range(i + 1, len(regions)):
                if j in used:
                    continue
                
                rx1, ry1, rx2, ry2 = regions[j]
                
                # 检查重叠
                if not (x2 < rx1 or x1 > rx2 or y2 < ry1 or y1 > ry2):
                    # 合并
                    x1 = min(x1, rx1)
                    y1 = min(y1, ry1)
                    x2 = max(x2, rx2)
                    y2 = max(y2, ry2)
                    used.add(j)
            
            merged.append((x1, y1, x2, y2))
            used.add(i)
        
        return merged
    
    def remove_watermark(self, image_path, output_path, regions):
        """
        去除水印
        """
        try:
            with Image.open(image_path) as img:
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # 对每个区域进行处理
                for region in regions:
                    x1, y1, x2, y2 = region
                    
                    # 提取区域
                    roi = img.crop((x1, y1, x2, y2))
                    
                    # 简单修复：使用高斯模糊
                    blurred_roi = roi.filter(ImageFilter.GaussianBlur(radius=3))
                    
                    # 降低对比度使其更接近背景
                    enhancer = ImageEnhance.Contrast(blurred_roi)
                    reduced_contrast = enhancer.enhance(0.7)  # 降低对比度
                    
                    # 调整亮度
                    brightness_enhancer = ImageEnhance.Brightness(reduced_contrast)
                    adjusted_roi = brightness_enhancer.enhance(1.1)  # 稍微提亮
                    
                    # 将处理后的区域粘贴回去
                    img.paste(adjusted_roi, (x1, y1))
                
                # 保存结果
                img.save(output_path, quality=95)
                return True
                
        except Exception as e:
            logger.error(f"修复失败 {image_path}: {e}")
            return False
    
    def process_single_image(self, input_path, output_path):
        """
        处理单张图片
        """
        try:
            logger.info(f"处理: {input_path}")
            
            # 检测水印区域
            regions = self.detect_watermark_regions(input_path)
            
            if not regions:
                logger.info(f"未检测到水印，复制原图: {input_path}")
                import shutil
                shutil.copy2(input_path, output_path)
                return True
            
            # 去除水印
            success = self.remove_watermark(input_path, output_path, regions)
            
            if success:
                logger.info(f"处理完成: {output_path}")
            
            return success
            
        except Exception as e:
            logger.error(f"处理失败 {input_path}: {e}")
            return False
    
    def process_batch(self, input_dir, output_dir):
        """
        批量处理
        """
        input_path = Path(input_dir)
        output_path = Path(output_dir)
        
        if not input_path.exists():
            logger.error(f"输入目录不存在: {input_dir}")
            return
        
        # 创建输出目录
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 收集图片文件
        supported_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
        image_files = []
        for ext in supported_formats:
            image_files.extend(input_path.glob(f"*{ext}"))
            image_files.extend(input_path.glob(f"*{ext.upper()}"))
        
        if not image_files:
            logger.error(f"在 {input_dir} 中未找到支持的图片文件")
            return
        
        logger.info(f"找到 {len(image_files)} 个图片文件")
        
        success_count = 0
        for img_file in image_files:
            output_file = output_path / img_file.name
            if self.process_single_image(img_file, output_file):
                success_count += 1
            self.processed_count += 1
        
        logger.info("=" * 50)
        logger.info("批量处理完成!")
        logger.info(f"总文件数: {len(image_files)}")
        logger.info(f"成功处理: {success_count}")
        logger.info(f"处理失败: {len(image_files) - success_count}")
        logger.info(f"输出目录: {output_dir}")

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='简化版水印去除工具')
    parser.add_argument('--input', '-i', required=True, help='输入目录或文件')
    parser.add_argument('--output', '-o', required=True, help='输出目录或文件')
    
    args = parser.parse_args()
    
    remover = SimpleWatermarkRemover()
    
    logger.info("🚀 简化版水印去除工具启动")
    logger.info("=" * 50)
    
    input_path = Path(args.input)
    output_path = Path(args.output)
    
    if input_path.is_file():
        # 处理单个文件
        output_path.parent.mkdir(parents=True, exist_ok=True)
        remover.process_single_image(input_path, output_path)
    else:
        # 批量处理
        remover.process_batch(args.input, args.output)

if __name__ == "__main__":
    main()
