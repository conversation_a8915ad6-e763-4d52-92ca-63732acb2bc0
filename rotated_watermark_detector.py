#!/usr/bin/env python3
"""
倾斜水印检测和去除工具
专门处理倾斜的 WWW.ABSKOOP.COM 水印
结合模板匹配、霍夫变换和形态学操作
"""

import cv2
import numpy as np
import argparse
from pathlib import Path
import logging
import math

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RotatedWatermarkDetector:
    def __init__(self):
        self.target_text = "WWW.ABSKOOP.COM"
        self.watermark_keywords = ["ABSKOOP", "WWW", "COM"]
    
    def detect_rotated_watermarks(self, image):
        """
        检测倾斜的水印
        使用多种方法组合：模板匹配、霍夫变换、连通组件分析
        """
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        height, width = gray.shape
        
        logger.info("开始检测倾斜水印...")
        
        # 方法1: 检测倾斜文字的方向
        rotation_angles = self._detect_text_rotation_angles(gray)
        logger.info(f"检测到可能的旋转角度: {rotation_angles}")
        
        # 方法2: 基于亮度和对比度的初步筛选
        candidate_regions = self._detect_watermark_candidates(gray)
        logger.info(f"找到 {len(candidate_regions)} 个候选区域")
        
        # 方法3: 对每个候选区域进行旋转匹配
        watermark_regions = self._match_rotated_watermarks(gray, candidate_regions, rotation_angles)
        logger.info(f"确认 {len(watermark_regions)} 个水印区域")
        
        # 方法4: 创建精确的遮罩
        final_mask = self._create_precise_mask(gray, watermark_regions)
        
        return final_mask
    
    def _detect_text_rotation_angles(self, gray):
        """
        检测文字的旋转角度
        使用霍夫变换检测直线，推断文字方向
        """
        # 边缘检测
        edges = cv2.Canny(gray, 30, 100)
        
        # 霍夫直线检测
        lines = cv2.HoughLines(edges, 1, np.pi/180, threshold=100)
        
        angles = []
        if lines is not None:
            for rho, theta in lines[:20]:  # 只取前20条直线
                angle = theta * 180 / np.pi
                # 转换为文字可能的角度范围
                if angle > 90:
                    angle = angle - 180
                angles.append(angle)
        
        # 聚类相似的角度
        unique_angles = []
        for angle in angles:
            # 只保留-45到45度范围内的角度
            if -45 <= angle <= 45:
                # 检查是否已有相似角度
                is_similar = False
                for existing_angle in unique_angles:
                    if abs(angle - existing_angle) < 5:  # 5度容差
                        is_similar = True
                        break
                if not is_similar:
                    unique_angles.append(angle)
        
        # 添加一些常见的水印角度
        common_angles = [0, 15, 30, -15, -30, 45, -45]
        for angle in common_angles:
            if angle not in unique_angles:
                unique_angles.append(angle)
        
        return unique_angles[:10]  # 最多返回10个角度
    
    def _detect_watermark_candidates(self, gray):
        """
        检测水印候选区域
        基于亮度、对比度和尺寸特征
        """
        # 1. 亮度筛选 - 水印通常在特定亮度范围
        brightness_mask = cv2.inRange(gray, 180, 230)
        
        # 2. 对比度筛选 - 水印通常对比度较低
        kernel = np.ones((15, 15), np.float32) / 225
        local_mean = cv2.filter2D(gray.astype(np.float32), -1, kernel)
        local_var = cv2.filter2D((gray.astype(np.float32) - local_mean)**2, -1, kernel)
        local_std = np.sqrt(np.maximum(local_var, 0))
        
        low_contrast_mask = (local_std < 25).astype(np.uint8) * 255
        
        # 3. 组合筛选条件
        candidate_mask = cv2.bitwise_and(brightness_mask, low_contrast_mask)
        
        # 4. 形态学操作清理
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
        candidate_mask = cv2.morphologyEx(candidate_mask, cv2.MORPH_OPEN, kernel, iterations=1)
        candidate_mask = cv2.morphologyEx(candidate_mask, cv2.MORPH_CLOSE, kernel, iterations=2)
        
        # 5. 查找连通区域
        contours, _ = cv2.findContours(candidate_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        candidates = []
        for contour in contours:
            area = cv2.contourArea(contour)
            x, y, w, h = cv2.boundingRect(contour)
            
            # 筛选合理大小的区域
            if 100 < area < 5000 and w > 20 and h > 5:
                candidates.append({
                    'contour': contour,
                    'bbox': (x, y, w, h),
                    'area': area
                })
        
        return candidates
    
    def _match_rotated_watermarks(self, gray, candidates, angles):
        """
        对候选区域进行旋转模板匹配
        """
        watermark_regions = []
        
        # 创建 ABSKOOP 的简单模板
        template = self._create_abskoop_template()
        
        for candidate in candidates:
            x, y, w, h = candidate['bbox']
            
            # 扩展区域以包含完整的水印
            margin = 20
            x1 = max(0, x - margin)
            y1 = max(0, y - margin)
            x2 = min(gray.shape[1], x + w + margin)
            y2 = min(gray.shape[0], y + h + margin)
            
            roi = gray[y1:y2, x1:x2]
            
            if roi.size == 0:
                continue
            
            # 对每个角度进行匹配
            best_match_score = 0
            best_angle = 0
            
            for angle in angles:
                # 旋转ROI
                rotated_roi = self._rotate_image(roi, angle)
                
                if rotated_roi.size == 0:
                    continue
                
                # 模板匹配
                match_score = self._template_match_score(rotated_roi, template)
                
                if match_score > best_match_score:
                    best_match_score = match_score
                    best_angle = angle
            
            # 如果匹配分数足够高，认为是水印
            if best_match_score > 0.3:  # 调整这个阈值
                watermark_regions.append({
                    'bbox': (x1, y1, x2-x1, y2-y1),
                    'angle': best_angle,
                    'score': best_match_score,
                    'original_candidate': candidate
                })
                logger.info(f"找到水印: 位置({x1},{y1}), 角度{best_angle:.1f}°, 分数{best_match_score:.3f}")
        
        return watermark_regions
    
    def _create_abskoop_template(self):
        """
        创建 ABSKOOP 的简单模板
        """
        # 创建一个简单的文字模板
        template = np.ones((30, 150), dtype=np.uint8) * 200
        
        # 使用OpenCV绘制简单的文字形状
        cv2.putText(template, 'ABSKOOP', (5, 20), cv2.FONT_HERSHEY_SIMPLEX, 0.6, 180, 1)
        
        return template
    
    def _rotate_image(self, image, angle):
        """
        旋转图像
        """
        if abs(angle) < 0.1:  # 角度太小，不需要旋转
            return image
        
        height, width = image.shape[:2]
        center = (width // 2, height // 2)
        
        # 计算旋转矩阵
        rotation_matrix = cv2.getRotationMatrix2D(center, angle, 1.0)
        
        # 计算新的边界框大小
        cos_val = abs(rotation_matrix[0, 0])
        sin_val = abs(rotation_matrix[0, 1])
        new_width = int((height * sin_val) + (width * cos_val))
        new_height = int((height * cos_val) + (width * sin_val))
        
        # 调整旋转矩阵的平移部分
        rotation_matrix[0, 2] += (new_width / 2) - center[0]
        rotation_matrix[1, 2] += (new_height / 2) - center[1]
        
        # 执行旋转
        rotated = cv2.warpAffine(image, rotation_matrix, (new_width, new_height), 
                                borderValue=255)
        
        return rotated
    
    def _template_match_score(self, image, template):
        """
        计算模板匹配分数
        """
        if image.shape[0] < template.shape[0] or image.shape[1] < template.shape[1]:
            return 0
        
        # 归一化互相关匹配
        result = cv2.matchTemplate(image, template, cv2.TM_CCOEFF_NORMED)
        
        # 返回最大匹配值
        _, max_val, _, _ = cv2.minMaxLoc(result)
        
        return max_val
    
    def _create_precise_mask(self, gray, watermark_regions):
        """
        创建精确的水印遮罩
        """
        mask = np.zeros_like(gray)
        
        for region in watermark_regions:
            x, y, w, h = region['bbox']
            
            # 在遮罩上标记水印区域
            mask[y:y+h, x:x+w] = 255
        
        # 形态学操作优化遮罩
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel, iterations=2)
        
        return mask
    
    def remove_watermarks(self, image, mask):
        """
        去除水印
        """
        logger.info("开始去除水印...")
        
        # 检查遮罩大小
        mask_area = np.sum(mask == 255)
        total_area = mask.shape[0] * mask.shape[1]
        mask_ratio = mask_area / total_area
        
        logger.info(f"水印区域占比: {mask_ratio:.3%}")
        
        if mask_ratio == 0:
            logger.info("未检测到水印")
            return image
        
        # 使用inpainting去除水印
        if mask_ratio < 0.01:
            result = cv2.inpaint(image, mask, 3, cv2.INPAINT_TELEA)
        elif mask_ratio < 0.05:
            result = cv2.inpaint(image, mask, 5, cv2.INPAINT_TELEA)
        else:
            result = cv2.inpaint(image, mask, 7, cv2.INPAINT_NS)
        
        logger.info("水印去除完成")
        return result
    
    def process_image(self, input_path, output_path, save_debug=False):
        """
        处理图片
        """
        try:
            logger.info(f"开始处理: {input_path}")
            
            # 读取图片
            image = cv2.imread(str(input_path))
            if image is None:
                logger.error(f"无法读取图片: {input_path}")
                return False
            
            logger.info(f"图片尺寸: {image.shape}")
            
            # 检测倾斜水印
            mask = self.detect_rotated_watermarks(image)
            
            # 保存调试信息
            if save_debug:
                debug_dir = output_path.parent / "debug"
                debug_dir.mkdir(exist_ok=True)
                
                mask_path = debug_dir / f"{output_path.stem}_rotated_mask.png"
                cv2.imwrite(str(mask_path), mask)
                logger.info(f"遮罩已保存: {mask_path}")
                
                # 创建可视化
                viz = image.copy()
                viz[mask == 255] = [0, 0, 255]  # 红色标记水印区域
                viz_path = debug_dir / f"{output_path.stem}_rotated_visualization.jpg"
                cv2.imwrite(str(viz_path), viz)
                logger.info(f"可视化已保存: {viz_path}")
            
            # 去除水印
            result = self.remove_watermarks(image, mask)
            
            # 保存结果
            cv2.imwrite(str(output_path), result)
            logger.info(f"处理完成: {output_path}")
            
            return True
            
        except Exception as e:
            logger.error(f"处理图片时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

def main():
    parser = argparse.ArgumentParser(description='倾斜水印检测和去除工具')
    parser.add_argument('--input', '-i', required=True, help='输入图片文件')
    parser.add_argument('--output', '-o', required=True, help='输出文件')
    parser.add_argument('--debug', action='store_true', help='保存调试信息')
    
    args = parser.parse_args()
    
    detector = RotatedWatermarkDetector()
    
    input_path = Path(args.input)
    output_path = Path(args.output)
    
    logger.info("🔄 倾斜水印检测工具启动")
    logger.info("=" * 50)
    
    if input_path.is_file():
        detector.process_image(input_path, output_path, args.debug)
    else:
        logger.error(f"输入路径不存在: {input_path}")

if __name__ == "__main__":
    main()
