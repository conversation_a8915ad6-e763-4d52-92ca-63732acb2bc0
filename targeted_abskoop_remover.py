#!/usr/bin/env python3
"""
基于分析结果的精确 abskoop 水印去除工具
根据你的图片特征调整的专门版本
"""

import cv2
import numpy as np
import os
import argparse
from pathlib import Path
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TargetedAbskoopRemover:
    def __init__(self):
        self.supported_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
    
    def detect_abskoop_targeted(self, image):
        """
        基于你的图片分析结果的精确检测
        平均亮度: 239.1, 建议范围: 190-230
        """
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        height, width = gray.shape
        
        logger.info("开始基于分析结果的精确检测...")
        
        # 方法1: 使用分析建议的亮度范围 (190-230)
        mask1 = cv2.inRange(gray, 190, 230)
        
        # 方法2: 更保守的范围，专门针对半透明水印
        mask2 = cv2.inRange(gray, 200, 225)
        
        # 方法3: 检测文字边缘 (使用分析建议的参数)
        edges = cv2.Canny(gray, 20, 60)
        
        # 膨胀边缘以连接文字
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
        edges_dilated = cv2.dilate(edges, kernel, iterations=1)
        
        # 方法4: 检测特定大小的文字区域
        mask4 = self._detect_watermark_sized_text(gray)
        
        # 智能组合 - 使用投票机制
        combined_mask = self._smart_voting([mask1, mask2, edges_dilated, mask4])
        
        # 精确后处理
        final_mask = self._precise_post_process(combined_mask, gray)
        
        logger.info("精确检测完成")
        return final_mask
    
    def _detect_watermark_sized_text(self, gray):
        """检测水印大小的文字区域"""
        # 使用自适应阈值检测文字
        adaptive_thresh = cv2.adaptiveThreshold(
            gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
        )
        
        # 查找轮廓
        contours, _ = cv2.findContours(adaptive_thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        mask = np.zeros_like(gray)
        
        for contour in contours:
            area = cv2.contourArea(contour)
            x, y, w, h = cv2.boundingRect(contour)
            aspect_ratio = w / h if h > 0 else 0
            
            # 提取区域分析亮度
            roi = gray[y:y+h, x:x+w]
            mean_brightness = np.mean(roi)
            
            # 水印文字的特征 (基于分析结果调整)
            is_watermark_candidate = (
                # 面积范围 (不要太大的区域)
                100 < area < 2000 and
                # 宽高比 (文字特征)
                1 < aspect_ratio < 8 and
                # 亮度范围 (基于你的图片分析)
                190 < mean_brightness < 230 and
                # 尺寸限制 (避免大的正常文字)
                w < 200 and h < 50
            )
            
            if is_watermark_candidate:
                cv2.fillPoly(mask, [contour], 255)
        
        return mask
    
    def _smart_voting(self, masks):
        """智能投票机制组合遮罩"""
        if not masks:
            return np.zeros((100, 100), dtype=np.uint8)
        
        # 计算每个像素的投票数
        vote_map = np.zeros_like(masks[0], dtype=np.float32)
        
        # 给不同方法不同的权重
        weights = [0.3, 0.4, 0.2, 0.1]  # 对应 mask1, mask2, edges, text
        
        for i, mask in enumerate(masks):
            if i < len(weights):
                vote_map += (mask > 0).astype(np.float32) * weights[i]
        
        # 只有获得足够投票的区域才被认为是水印
        threshold = 0.4  # 调整这个值来控制检测的严格程度
        final_mask = (vote_map >= threshold).astype(np.uint8) * 255
        
        return final_mask
    
    def _precise_post_process(self, mask, gray):
        """精确后处理"""
        # 去除小的噪声点
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel, iterations=1)
        
        # 分析每个连通区域
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        filtered_mask = np.zeros_like(gray)
        
        for contour in contours:
            area = cv2.contourArea(contour)
            x, y, w, h = cv2.boundingRect(contour)
            
            # 提取区域进行详细分析
            roi = gray[y:y+h, x:x+w]
            mean_brightness = np.mean(roi)
            std_brightness = np.std(roi)
            
            # 更严格的水印判断
            is_likely_watermark = (
                # 面积不能太大 (避免误检大块内容)
                50 < area < 1500 and
                # 亮度特征 (基于你的图片)
                195 < mean_brightness < 225 and
                # 对比度特征 (水印通常对比度较低)
                std_brightness < 25 and
                # 尺寸限制
                w < 150 and h < 40
            )
            
            if is_likely_watermark:
                cv2.fillPoly(filtered_mask, [contour], 255)
        
        # 轻微膨胀以确保覆盖边缘
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (2, 2))
        filtered_mask = cv2.dilate(filtered_mask, kernel, iterations=1)
        
        return filtered_mask
    
    def remove_watermark_conservative(self, image, mask):
        """保守的水印去除"""
        logger.info("开始保守水印去除...")
        
        # 检查遮罩大小
        mask_area = np.sum(mask == 255)
        total_area = mask.shape[0] * mask.shape[1]
        mask_ratio = mask_area / total_area
        
        logger.info(f"将要修复的区域占比: {mask_ratio:.3%}")
        
        if mask_ratio == 0:
            logger.warning("没有检测到需要修复的区域")
            return image
        elif mask_ratio > 0.02:
            logger.warning("检测到的区域较大，请检查是否误检了正常内容")
        
        # 使用保守的修复方法
        if mask_ratio < 0.005:
            # 极小区域 - 最精细修复
            result = cv2.inpaint(image, mask, 2, cv2.INPAINT_TELEA)
            logger.info("使用极精细修复 (radius=2)")
        elif mask_ratio < 0.01:
            # 小区域 - 精细修复
            result = cv2.inpaint(image, mask, 3, cv2.INPAINT_TELEA)
            logger.info("使用精细修复 (radius=3)")
        else:
            # 较大区域 - 标准修复
            result = cv2.inpaint(image, mask, 5, cv2.INPAINT_TELEA)
            logger.info("使用标准修复 (radius=5)")
        
        logger.info("保守水印去除完成")
        return result
    
    def process_single_image(self, input_path, output_path, save_debug=False):
        """处理单张图片"""
        try:
            logger.info(f"开始处理: {input_path}")
            
            # 读取图片
            image = cv2.imread(str(input_path))
            if image is None:
                logger.error(f"无法读取图片: {input_path}")
                return False
            
            logger.info(f"图片尺寸: {image.shape}")
            
            # 精确检测水印
            mask = self.detect_abskoop_targeted(image)
            
            # 检查遮罩质量
            mask_area = np.sum(mask == 255)
            total_area = mask.shape[0] * mask.shape[1]
            mask_ratio = mask_area / total_area
            
            logger.info(f"检测到的水印区域占比: {mask_ratio:.3%}")
            
            if mask_ratio == 0:
                logger.info("未检测到水印区域，返回原图")
                # 直接复制原图
                import shutil
                shutil.copy2(input_path, output_path)
                return True
            
            # 保存调试信息
            if save_debug:
                debug_dir = output_path.parent / "debug"
                debug_dir.mkdir(exist_ok=True)
                
                mask_path = debug_dir / f"{output_path.stem}_targeted_mask.png"
                cv2.imwrite(str(mask_path), mask)
                logger.info(f"精确遮罩已保存: {mask_path}")
            
            # 保守去除水印
            result = self.remove_watermark_conservative(image, mask)
            
            # 保存结果
            cv2.imwrite(str(output_path), result)
            logger.info(f"处理完成: {output_path}")
            
            return True
            
        except Exception as e:
            logger.error(f"处理图片时出错 {input_path}: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

def main():
    parser = argparse.ArgumentParser(description='基于分析结果的精确abskoop水印去除工具')
    parser.add_argument('--input', '-i', required=True, help='输入图片文件')
    parser.add_argument('--output', '-o', required=True, help='输出文件')
    parser.add_argument('--debug', action='store_true', help='保存调试信息')
    
    args = parser.parse_args()
    
    remover = TargetedAbskoopRemover()
    
    input_path = Path(args.input)
    output_path = Path(args.output)
    
    logger.info("🎯 基于分析结果的精确水印去除工具启动")
    logger.info("=" * 50)
    
    if input_path.is_file():
        # 处理单个文件
        remover.process_single_image(input_path, output_path, args.debug)
    else:
        logger.error(f"输入路径不存在: {input_path}")

if __name__ == "__main__":
    main()
