#!/usr/bin/env python3
"""
终极水印去除工具
使用像素级分析和智能填充
"""

import os
import sys
from pathlib import Path
import logging
import numpy as np
from PIL import Image, ImageFilter, ImageEnhance
import cv2

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class UltimateWatermarkRemover:
    def __init__(self, grid_size=200):
        self.grid_size = grid_size
        
    def get_grid_coordinates(self, grid_id, image_width, image_height):
        """根据网格ID计算实际坐标"""
        grids_per_row = (image_width + self.grid_size - 1) // self.grid_size
        row = (grid_id - 1) // grids_per_row
        col = (grid_id - 1) % grids_per_row
        
        x = col * self.grid_size
        y = row * self.grid_size
        x2 = min(x + self.grid_size, image_width)
        y2 = min(y + self.grid_size, image_height)
        
        return (x, y, x2, y2)
    
    def detect_watermark_pixels(self, region_array):
        """
        检测水印像素 - 基于灰度和对比度分析
        """
        # 转换为灰度
        if len(region_array.shape) == 3:
            gray = np.dot(region_array[...,:3], [0.2989, 0.5870, 0.1140])
        else:
            gray = region_array
        
        # 计算局部统计
        mean_val = np.mean(gray)
        std_val = np.std(gray)
        
        # 水印像素通常在特定灰度范围内
        # 基于之前的分析，水印是浅灰色
        watermark_mask = np.zeros_like(gray, dtype=bool)
        
        # 方法1: 基于灰度范围
        gray_mask = (gray > 180) & (gray < 240)
        
        # 方法2: 基于局部对比度
        # 使用形态学操作检测边缘
        kernel = np.ones((3,3), np.uint8)
        gray_uint8 = gray.astype(np.uint8)
        
        # 检测边缘
        edges = cv2.Canny(gray_uint8, 50, 150)
        
        # 膨胀边缘以包含水印区域
        dilated_edges = cv2.dilate(edges, kernel, iterations=2)
        
        # 结合灰度和边缘信息
        watermark_mask = gray_mask & (dilated_edges > 0)
        
        return watermark_mask
    
    def intelligent_fill(self, region_array, watermark_mask):
        """
        智能填充水印区域
        """
        result = region_array.copy()
        
        # 对每个颜色通道分别处理
        for channel in range(3):
            channel_data = region_array[:, :, channel].astype(np.float32)
            
            # 使用OpenCV的inpaint功能
            mask_uint8 = watermark_mask.astype(np.uint8) * 255
            
            # 尝试不同的修复方法
            try:
                # 方法1: Navier-Stokes based inpainting
                inpainted = cv2.inpaint(channel_data.astype(np.uint8), mask_uint8, 3, cv2.INPAINT_NS)
                result[:, :, channel] = inpainted
            except:
                # 方法2: 简单的邻域平均
                result[:, :, channel] = self.simple_fill(channel_data, watermark_mask)
        
        return result.astype(np.uint8)
    
    def simple_fill(self, channel_data, mask):
        """
        简单的邻域填充方法
        """
        result = channel_data.copy()
        
        # 找到需要填充的像素
        fill_coords = np.where(mask)
        
        for y, x in zip(fill_coords[0], fill_coords[1]):
            # 获取周围非水印像素的平均值
            neighbors = []
            
            for dy in [-2, -1, 0, 1, 2]:
                for dx in [-2, -1, 0, 1, 2]:
                    ny, nx = y + dy, x + dx
                    if (0 <= ny < mask.shape[0] and 
                        0 <= nx < mask.shape[1] and 
                        not mask[ny, nx]):
                        neighbors.append(channel_data[ny, nx])
            
            if neighbors:
                result[y, x] = np.mean(neighbors)
            else:
                # 如果没有邻居，使用全局平均
                result[y, x] = np.mean(channel_data[~mask])
        
        return result
    
    def remove_watermark_ultimate(self, image, region_coords):
        """
        终极水印去除方法
        """
        x1, y1, x2, y2 = region_coords
        
        # 提取区域
        region = image.crop((x1, y1, x2, y2))
        region_array = np.array(region)
        
        logger.info(f"终极处理区域 ({x1}, {y1}, {x2}, {y2}) 尺寸: {x2-x1}x{y2-y1}")
        
        # 检测水印像素
        watermark_mask = self.detect_watermark_pixels(region_array)
        
        # 如果检测到水印像素
        if np.any(watermark_mask):
            logger.info(f"检测到 {np.sum(watermark_mask)} 个水印像素")
            
            # 智能填充
            filled_array = self.intelligent_fill(region_array, watermark_mask)
            
            # 转换回PIL图像
            filled_region = Image.fromarray(filled_array)
            
            # 轻微后处理
            final_region = self.post_process(filled_region)
        else:
            logger.info("未检测到明显水印，使用标准处理")
            final_region = self.standard_process(region)
        
        # 粘贴回原图
        image.paste(final_region, (x1, y1))
        
        return image
    
    def post_process(self, region):
        """
        后处理 - 轻微平滑
        """
        # 轻微高斯模糊
        smoothed = region.filter(ImageFilter.GaussianBlur(radius=0.8))
        
        # 轻微亮度调整
        enhancer = ImageEnhance.Brightness(smoothed)
        final_region = enhancer.enhance(1.02)
        
        return final_region
    
    def standard_process(self, region):
        """
        标准处理方法（当检测不到水印时）
        """
        # 模糊
        blurred = region.filter(ImageFilter.GaussianBlur(radius=2.0))
        
        # 亮度调整
        enhancer = ImageEnhance.Brightness(blurred)
        brightened = enhancer.enhance(1.15)
        
        # 对比度调整
        contrast_enhancer = ImageEnhance.Contrast(brightened)
        final_region = contrast_enhancer.enhance(0.85)
        
        return final_region
    
    def process_image_ultimate(self, input_path, output_path, grid_ids):
        """
        使用终极方法处理图片
        """
        try:
            with Image.open(input_path) as img:
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                width, height = img.size
                logger.info(f"终极处理: {input_path} ({width}x{height})")
                logger.info(f"目标网格: {grid_ids}")
                
                # 处理每个指定的网格
                for grid_id in grid_ids:
                    coords = self.get_grid_coordinates(grid_id, width, height)
                    img = self.remove_watermark_ultimate(img, coords)
                
                # 保存结果
                img.save(output_path, quality=98)
                logger.info(f"终极处理完成: {output_path}")
                
                return True
                
        except Exception as e:
            logger.error(f"终极处理失败 {input_path}: {e}")
            return False

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='终极水印去除工具')
    parser.add_argument('--input', '-i', required=True, help='输入图片路径')
    parser.add_argument('--output', '-o', required=True, help='输出图片路径')
    parser.add_argument('--grids', '-g', required=True, help='网格ID列表，用逗号分隔')
    
    args = parser.parse_args()
    
    # 解析网格ID
    try:
        grid_ids = [int(x.strip()) for x in args.grids.split(',')]
    except ValueError:
        logger.error("网格ID格式错误")
        sys.exit(1)
    
    remover = UltimateWatermarkRemover()
    
    logger.info("🚀 终极水印去除工具启动")
    logger.info("=" * 50)
    
    # 检查OpenCV
    try:
        import cv2
        logger.info(f"OpenCV版本: {cv2.__version__}")
    except ImportError:
        logger.warning("OpenCV未安装，将使用简化版本")
    
    # 处理图片
    success = remover.process_image_ultimate(args.input, args.output, grid_ids)
    
    if success:
        logger.info("✅ 终极处理完成！")
    else:
        logger.error("❌ 终极处理失败！")
        sys.exit(1)

if __name__ == "__main__":
    main()
