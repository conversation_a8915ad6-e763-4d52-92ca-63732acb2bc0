#!/usr/bin/env python3
"""
手动mask创建工具
让用户精确标记水印区域
"""

import cv2
import numpy as np
import argparse
from pathlib import Path

class ManualMaskCreator:
    def __init__(self):
        self.drawing = False
        self.brush_size = 10
        self.mask = None
        self.original = None
        self.display_image = None
        
    def mouse_callback(self, event, x, y, flags, param):
        """鼠标回调函数"""
        if event == cv2.EVENT_LBUTTONDOWN:
            self.drawing = True
            cv2.circle(self.mask, (x, y), self.brush_size, 255, -1)
            cv2.circle(self.display_image, (x, y), self.brush_size, (0, 255, 0), -1)
            
        elif event == cv2.EVENT_MOUSEMOVE and self.drawing:
            cv2.circle(self.mask, (x, y), self.brush_size, 255, -1)
            cv2.circle(self.display_image, (x, y), self.brush_size, (0, 255, 0), -1)
            
        elif event == cv2.EVENT_LBUTTONUP:
            self.drawing = False
            
        elif event == cv2.EVENT_RBUTTONDOWN:
            # 右键擦除
            cv2.circle(self.mask, (x, y), self.brush_size, 0, -1)
            # 恢复原图区域
            roi = self.original[y-self.brush_size:y+self.brush_size, 
                              x-self.brush_size:x+self.brush_size]
            if roi.size > 0:
                self.display_image[y-self.brush_size:y+self.brush_size, 
                                 x-self.brush_size:x+self.brush_size] = roi
    
    def create_mask_interactive(self, image_path, output_mask_path):
        """交互式创建mask"""
        # 读取图片
        self.original = cv2.imread(str(image_path))
        if self.original is None:
            print(f"❌ 无法读取图片: {image_path}")
            return False
        
        height, width = self.original.shape[:2]
        
        # 如果图片太大，缩放显示
        max_display_size = 1200
        if width > max_display_size or height > max_display_size:
            scale = min(max_display_size / width, max_display_size / height)
            display_width = int(width * scale)
            display_height = int(height * scale)
            self.display_image = cv2.resize(self.original, (display_width, display_height))
            # mask也要对应缩放
            self.mask = np.zeros((display_height, display_width), dtype=np.uint8)
            scale_factor = scale
        else:
            self.display_image = self.original.copy()
            self.mask = np.zeros((height, width), dtype=np.uint8)
            scale_factor = 1.0
        
        # 创建窗口
        cv2.namedWindow('Manual Mask Creator', cv2.WINDOW_NORMAL)
        cv2.setMouseCallback('Manual Mask Creator', self.mouse_callback)
        
        print("🎯 手动mask创建工具")
        print("=" * 40)
        print("操作说明:")
        print("  左键拖拽: 标记水印区域 (绿色)")
        print("  右键拖拽: 擦除标记")
        print("  +/-: 调整画笔大小")
        print("  s: 保存mask")
        print("  r: 重置mask")
        print("  q: 退出")
        print("  空格: 切换显示模式")
        
        show_mask_overlay = False
        
        while True:
            # 显示图像
            if show_mask_overlay:
                # 显示mask叠加
                overlay = self.display_image.copy()
                mask_colored = cv2.applyColorMap(self.mask, cv2.COLORMAP_JET)
                overlay = cv2.addWeighted(overlay, 0.7, mask_colored, 0.3, 0)
                cv2.imshow('Manual Mask Creator', overlay)
            else:
                cv2.imshow('Manual Mask Creator', self.display_image)
            
            key = cv2.waitKey(1) & 0xFF
            
            if key == ord('q'):
                break
            elif key == ord('s'):
                # 保存mask
                if scale_factor != 1.0:
                    # 如果缩放了，需要将mask恢复到原始尺寸
                    final_mask = cv2.resize(self.mask, (width, height), interpolation=cv2.INTER_NEAREST)
                else:
                    final_mask = self.mask
                
                cv2.imwrite(str(output_mask_path), final_mask)
                print(f"✅ Mask已保存: {output_mask_path}")
                
                # 计算mask统计
                mask_area = np.sum(final_mask == 255)
                total_area = final_mask.shape[0] * final_mask.shape[1]
                mask_ratio = mask_area / total_area
                print(f"   标记区域占比: {mask_ratio:.2%}")
                
            elif key == ord('r'):
                # 重置mask
                self.mask.fill(0)
                self.display_image = cv2.resize(self.original, self.display_image.shape[:2][::-1]) if scale_factor != 1.0 else self.original.copy()
                print("🔄 Mask已重置")
                
            elif key == ord('+') or key == ord('='):
                self.brush_size = min(50, self.brush_size + 2)
                print(f"🖌️ 画笔大小: {self.brush_size}")
                
            elif key == ord('-'):
                self.brush_size = max(2, self.brush_size - 2)
                print(f"🖌️ 画笔大小: {self.brush_size}")
                
            elif key == ord(' '):
                show_mask_overlay = not show_mask_overlay
                mode = "叠加显示" if show_mask_overlay else "正常显示"
                print(f"👁️ 切换到: {mode}")
        
        cv2.destroyAllWindows()
        return True
    
    def apply_mask_and_inpaint(self, image_path, mask_path, output_path):
        """应用mask并进行修复"""
        # 读取图片和mask
        image = cv2.imread(str(image_path))
        mask = cv2.imread(str(mask_path), cv2.IMREAD_GRAYSCALE)
        
        if image is None or mask is None:
            print("❌ 无法读取图片或mask")
            return False
        
        # 确保尺寸一致
        if image.shape[:2] != mask.shape:
            mask = cv2.resize(mask, (image.shape[1], image.shape[0]), interpolation=cv2.INTER_NEAREST)
        
        print("🔧 开始修复...")
        
        # 计算mask统计
        mask_area = np.sum(mask == 255)
        total_area = mask.shape[0] * mask.shape[1]
        mask_ratio = mask_area / total_area
        
        print(f"   修复区域占比: {mask_ratio:.2%}")
        
        # 根据mask大小选择修复方法
        if mask_ratio < 0.01:
            # 小区域 - 精细修复
            result = cv2.inpaint(image, mask, 3, cv2.INPAINT_TELEA)
            print("   使用精细修复 (TELEA, radius=3)")
        elif mask_ratio < 0.05:
            # 中等区域 - 标准修复
            result = cv2.inpaint(image, mask, 5, cv2.INPAINT_TELEA)
            print("   使用标准修复 (TELEA, radius=5)")
        else:
            # 大区域 - 大半径修复
            result = cv2.inpaint(image, mask, 7, cv2.INPAINT_NS)
            print("   使用大半径修复 (NS, radius=7)")
        
        # 保存结果
        cv2.imwrite(str(output_path), result)
        print(f"✅ 修复完成: {output_path}")
        
        return True

def main():
    parser = argparse.ArgumentParser(description='手动mask创建和水印去除工具')
    parser.add_argument('--input', '-i', required=True, help='输入图片文件')
    parser.add_argument('--mask', '-m', help='输出mask文件路径')
    parser.add_argument('--output', '-o', help='输出修复后的图片')
    parser.add_argument('--mode', choices=['create', 'apply'], default='create', 
                       help='模式: create=创建mask, apply=应用已有mask')
    
    args = parser.parse_args()
    
    creator = ManualMaskCreator()
    
    input_path = Path(args.input)
    
    if args.mode == 'create':
        # 创建mask模式
        if not args.mask:
            mask_path = input_path.parent / f"{input_path.stem}_manual_mask.png"
        else:
            mask_path = Path(args.mask)
        
        print(f"🎯 创建mask模式")
        print(f"   输入图片: {input_path}")
        print(f"   输出mask: {mask_path}")
        
        creator.create_mask_interactive(input_path, mask_path)
        
    elif args.mode == 'apply':
        # 应用mask模式
        if not args.mask or not args.output:
            print("❌ apply模式需要指定 --mask 和 --output 参数")
            return
        
        mask_path = Path(args.mask)
        output_path = Path(args.output)
        
        print(f"🔧 应用mask模式")
        print(f"   输入图片: {input_path}")
        print(f"   输入mask: {mask_path}")
        print(f"   输出图片: {output_path}")
        
        creator.apply_mask_and_inpaint(input_path, mask_path, output_path)

if __name__ == "__main__":
    main()
