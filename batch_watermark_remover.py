#!/usr/bin/env python3
"""
批量水印去除工具
专门处理大量包含 WWW.ABSKOOP.COM 水印的图片
结合多种检测方法，适合批量自动化处理
"""

import os
import sys
from pathlib import Path
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
import time

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 尝试导入依赖
try:
    from PIL import Image, ImageFilter, ImageEnhance
    import numpy as np
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    logger.warning("PIL/Pillow not available, using basic mode")

class BatchWatermarkRemover:
    def __init__(self):
        self.supported_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
        self.processed_count = 0
        self.success_count = 0
        self.error_count = 0
        
    def detect_watermark_regions_pil(self, image_path):
        """
        使用PIL检测水印区域
        基于亮度、对比度和区域特征
        """
        try:
            with Image.open(image_path) as img:
                # 转换为RGB
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # 转换为numpy数组进行分析
                img_array = np.array(img)
                
                # 转换为灰度
                gray = np.dot(img_array[...,:3], [0.2989, 0.5870, 0.1140])
                
                # 检测水印特征
                watermark_regions = self._analyze_watermark_features(gray, img.size)
                
                return watermark_regions
                
        except Exception as e:
            logger.error(f"PIL检测失败 {image_path}: {e}")
            return []
    
    def _analyze_watermark_features(self, gray, image_size):
        """
        分析图像特征，检测水印区域
        专门针对 WWW.ABSKOOP.COM 类型的半透明倾斜水印
        """
        width, height = image_size
        regions = []

        # 针对你截图中的水印特征调整参数
        # 1. 水印亮度范围 - 从截图看是中等灰色
        watermark_brightness_min = 120  # 降低最小值
        watermark_brightness_max = 180  # 降低最大值

        # 2. 使用更小的块来精确检测
        block_size = 50  # 减小块大小
        overlap = block_size // 4  # 增加重叠

        for y in range(0, height - block_size, overlap):
            for x in range(0, width - block_size, overlap):
                block = gray[y:y+block_size, x:x+block_size]

                if block.size == 0:
                    continue

                mean_brightness = np.mean(block)
                std_brightness = np.std(block)

                # 检测边缘密度（文字特征）
                edges = self._detect_edges_simple(block)
                edge_density = np.sum(edges > 0) / edges.size

                # 水印特征判断 - 更宽松的条件
                is_watermark_candidate = (
                    # 亮度范围
                    watermark_brightness_min < mean_brightness < watermark_brightness_max and
                    # 有一定的对比度变化（文字边缘）
                    5 < std_brightness < 40 and
                    # 有足够的边缘密度（文字特征）
                    edge_density > 0.02 and
                    # 排除过于复杂的区域（背景内容）
                    edge_density < 0.3
                )

                if is_watermark_candidate:
                    new_region = (x, y, x + block_size, y + block_size)
                    if not self._overlaps_with_existing(new_region, regions):
                        regions.append(new_region)

        # 3. 合并相邻区域
        merged_regions = self._merge_adjacent_regions(regions)

        # 4. 过滤区域 - 更宽松的大小限制
        filtered_regions = []
        total_area = width * height

        for region in merged_regions:
            x1, y1, x2, y2 = region
            area = (x2 - x1) * (y2 - y1)
            area_ratio = area / total_area

            # 保留更大范围的区域
            if 0.00005 < area_ratio < 0.1:  # 0.005% 到 10%
                filtered_regions.append(region)

        return filtered_regions

    def _detect_edges_simple(self, block):
        """
        简单的边缘检测
        """
        # 使用Sobel算子检测边缘
        sobel_x = np.array([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]])
        sobel_y = np.array([[-1, -2, -1], [0, 0, 0], [1, 2, 1]])

        # 计算梯度
        grad_x = self._convolve_simple(block, sobel_x)
        grad_y = self._convolve_simple(block, sobel_y)

        # 计算梯度幅值
        magnitude = np.sqrt(grad_x**2 + grad_y**2)

        return magnitude

    def _convolve_simple(self, image, kernel):
        """
        简单的卷积操作
        """
        h, w = image.shape
        kh, kw = kernel.shape

        # 输出图像
        output = np.zeros((h - kh + 1, w - kw + 1))

        for i in range(output.shape[0]):
            for j in range(output.shape[1]):
                output[i, j] = np.sum(image[i:i+kh, j:j+kw] * kernel)

        return output
    
    def _overlaps_with_existing(self, new_region, existing_regions):
        """检查新区域是否与现有区域重叠"""
        x1, y1, x2, y2 = new_region
        
        for existing in existing_regions:
            ex1, ey1, ex2, ey2 = existing
            
            # 检查重叠
            if not (x2 < ex1 or x1 > ex2 or y2 < ey1 or y1 > ey2):
                return True
        
        return False
    
    def _merge_adjacent_regions(self, regions):
        """合并相邻的区域"""
        if not regions:
            return []
        
        merged = []
        used = set()
        
        for i, region1 in enumerate(regions):
            if i in used:
                continue
            
            x1, y1, x2, y2 = region1
            
            # 查找可以合并的区域
            for j, region2 in enumerate(regions[i+1:], i+1):
                if j in used:
                    continue
                
                rx1, ry1, rx2, ry2 = region2
                
                # 检查是否相邻或重叠
                if (abs(x1 - rx2) < 50 or abs(x2 - rx1) < 50) and (abs(y1 - ry2) < 50 or abs(y2 - ry1) < 50):
                    # 合并区域
                    x1 = min(x1, rx1)
                    y1 = min(y1, ry1)
                    x2 = max(x2, rx2)
                    y2 = max(y2, ry2)
                    used.add(j)
            
            merged.append((x1, y1, x2, y2))
            used.add(i)
        
        return merged
    
    def remove_watermark_pil(self, image_path, output_path, regions):
        """
        使用PIL去除水印
        """
        try:
            with Image.open(image_path) as img:
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # 对每个区域进行修复
                for region in regions:
                    x1, y1, x2, y2 = region
                    
                    # 提取区域
                    roi = img.crop((x1, y1, x2, y2))
                    
                    # 简单的修复方法：使用周围像素的平均值
                    repaired_roi = self._repair_region_simple(img, (x1, y1, x2, y2))
                    
                    # 将修复后的区域粘贴回去
                    img.paste(repaired_roi, (x1, y1))
                
                # 保存结果
                img.save(output_path, quality=95)
                return True
                
        except Exception as e:
            logger.error(f"PIL修复失败 {image_path}: {e}")
            return False
    
    def _repair_region_simple(self, img, region):
        """
        改进的区域修复算法
        使用多种技术组合来更好地去除文字水印
        """
        x1, y1, x2, y2 = region
        width, height = img.size

        # 扩展边界以获取更多周围像素信息
        margin = 20  # 增加边距
        expand_x1 = max(0, x1 - margin)
        expand_y1 = max(0, y1 - margin)
        expand_x2 = min(width, x2 + margin)
        expand_y2 = min(height, y2 + margin)

        # 提取扩展区域
        expanded_roi = img.crop((expand_x1, expand_y1, expand_x2, expand_y2))

        # 转换为numpy数组进行处理
        expanded_array = np.array(expanded_roi)

        # 计算目标区域在扩展区域中的位置
        target_x1 = x1 - expand_x1
        target_y1 = y1 - expand_y1
        target_x2 = target_x1 + (x2 - x1)
        target_y2 = target_y1 + (y2 - y1)

        # 方法1: 使用周围像素的中值
        repaired_array = self._inpaint_median(expanded_array, target_x1, target_y1, target_x2, target_y2)

        # 方法2: 应用轻微模糊
        repaired_img = Image.fromarray(repaired_array.astype(np.uint8))
        repaired_img = repaired_img.filter(ImageFilter.GaussianBlur(radius=1.5))

        # 提取修复后的目标区域
        repaired_roi = repaired_img.crop((target_x1, target_y1, target_x2, target_y2))

        return repaired_roi

    def _inpaint_median(self, img_array, x1, y1, x2, y2):
        """
        使用中值滤波进行简单的图像修复
        """
        h, w, c = img_array.shape
        result = img_array.copy()

        # 对水印区域进行修复
        for y in range(max(0, y1), min(h, y2)):
            for x in range(max(0, x1), min(w, x2)):
                # 获取周围像素
                neighbors = []
                for dy in [-2, -1, 0, 1, 2]:
                    for dx in [-2, -1, 0, 1, 2]:
                        ny, nx = y + dy, x + dx
                        # 只使用水印区域外的像素
                        if (0 <= ny < h and 0 <= nx < w and
                            not (x1 <= nx < x2 and y1 <= ny < y2)):
                            neighbors.append(img_array[ny, nx])

                if neighbors:
                    # 使用中值作为修复值
                    neighbors = np.array(neighbors)
                    result[y, x] = np.median(neighbors, axis=0)

        return result
    
    def process_single_image(self, input_path, output_path):
        """处理单张图片"""
        try:
            logger.info(f"处理: {input_path}")
            
            if not PIL_AVAILABLE:
                logger.error("PIL不可用，无法处理图片")
                return False
            
            # 检测水印区域
            regions = self.detect_watermark_regions_pil(input_path)
            
            if not regions:
                logger.info(f"未检测到水印，复制原图: {input_path}")
                # 直接复制原图
                import shutil
                shutil.copy2(input_path, output_path)
                return True
            
            logger.info(f"检测到 {len(regions)} 个水印区域")
            
            # 去除水印
            success = self.remove_watermark_pil(input_path, output_path, regions)
            
            if success:
                logger.info(f"处理完成: {output_path}")
            
            return success
            
        except Exception as e:
            logger.error(f"处理失败 {input_path}: {e}")
            return False
    
    def process_batch(self, input_dir, output_dir, max_workers=4):
        """批量处理图片"""
        input_path = Path(input_dir)
        output_path = Path(output_dir)
        
        if not input_path.exists():
            logger.error(f"输入目录不存在: {input_dir}")
            return
        
        # 创建输出目录
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 收集所有图片文件
        image_files = []
        for ext in self.supported_formats:
            image_files.extend(input_path.glob(f"*{ext}"))
            image_files.extend(input_path.glob(f"*{ext.upper()}"))
        
        if not image_files:
            logger.error(f"在 {input_dir} 中未找到支持的图片文件")
            return
        
        logger.info(f"找到 {len(image_files)} 个图片文件")
        logger.info(f"使用 {max_workers} 个线程进行处理")
        
        start_time = time.time()
        
        # 使用线程池并行处理
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_file = {}
            for img_file in image_files:
                output_file = output_path / img_file.name
                future = executor.submit(self.process_single_image, img_file, output_file)
                future_to_file[future] = img_file
            
            # 处理结果
            for future in as_completed(future_to_file):
                img_file = future_to_file[future]
                self.processed_count += 1
                
                try:
                    success = future.result()
                    if success:
                        self.success_count += 1
                    else:
                        self.error_count += 1
                except Exception as e:
                    logger.error(f"处理异常 {img_file}: {e}")
                    self.error_count += 1
                
                # 显示进度
                if self.processed_count % 10 == 0:
                    progress = (self.processed_count / len(image_files)) * 100
                    logger.info(f"进度: {progress:.1f}% ({self.processed_count}/{len(image_files)})")
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 显示统计信息
        logger.info("=" * 50)
        logger.info("批量处理完成!")
        logger.info(f"总文件数: {len(image_files)}")
        logger.info(f"成功处理: {self.success_count}")
        logger.info(f"处理失败: {self.error_count}")
        logger.info(f"总耗时: {duration:.2f} 秒")
        logger.info(f"平均速度: {len(image_files)/duration:.2f} 文件/秒")
        logger.info(f"输出目录: {output_dir}")

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='批量水印去除工具')
    parser.add_argument('--input', '-i', required=True, help='输入目录')
    parser.add_argument('--output', '-o', required=True, help='输出目录')
    parser.add_argument('--workers', '-w', type=int, default=4, help='并行线程数 (默认: 4)')
    
    args = parser.parse_args()
    
    # 检查PIL可用性
    if not PIL_AVAILABLE:
        print("❌ 错误: 需要安装 Pillow")
        print("请运行: pip install Pillow numpy")
        sys.exit(1)
    
    remover = BatchWatermarkRemover()
    
    logger.info("🚀 批量水印去除工具启动")
    logger.info("=" * 50)
    
    remover.process_batch(args.input, args.output, args.workers)

if __name__ == "__main__":
    main()
