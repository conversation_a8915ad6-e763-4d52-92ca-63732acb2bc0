#!/usr/bin/env python3
"""
对比不同水印去除方法的效果
"""

import cv2
import numpy as np
import os
from pathlib import Path
import matplotlib.pyplot as plt
import matplotlib.patches as patches

def create_comparison_grid(original_path, results_dict, output_path):
    """
    创建对比网格图
    """
    # 读取原始图片
    original = cv2.imread(str(original_path))
    if original is None:
        print(f"无法读取原始图片: {original_path}")
        return
    
    original_rgb = cv2.cvtColor(original, cv2.COLOR_BGR2RGB)
    
    # 计算网格大小
    num_results = len(results_dict)
    cols = min(3, num_results + 1)  # 包括原图，最多3列
    rows = (num_results + 1 + cols - 1) // cols
    
    # 创建图形
    fig, axes = plt.subplots(rows, cols, figsize=(15, 5 * rows))
    if rows == 1:
        axes = [axes] if cols == 1 else axes
    else:
        axes = axes.flatten()
    
    # 显示原始图片
    axes[0].imshow(original_rgb)
    axes[0].set_title('原始图片 (带水印)', fontsize=12, fontweight='bold')
    axes[0].axis('off')
    
    # 显示处理结果
    for i, (method_name, result_path) in enumerate(results_dict.items(), 1):
        if i < len(axes):
            result = cv2.imread(str(result_path))
            if result is not None:
                result_rgb = cv2.cvtColor(result, cv2.COLOR_BGR2RGB)
                axes[i].imshow(result_rgb)
                axes[i].set_title(f'{method_name}', fontsize=12)
                axes[i].axis('off')
            else:
                axes[i].text(0.5, 0.5, f'无法加载\n{method_name}', 
                           ha='center', va='center', transform=axes[i].transAxes)
                axes[i].axis('off')
    
    # 隐藏多余的子图
    for i in range(num_results + 1, len(axes)):
        axes[i].axis('off')
    
    plt.tight_layout()
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f"对比图已保存: {output_path}")

def analyze_watermark_removal_quality(original_path, result_path):
    """
    分析水印去除质量
    """
    original = cv2.imread(str(original_path))
    result = cv2.imread(str(result_path))
    
    if original is None or result is None:
        return None
    
    # 确保尺寸一致
    if original.shape != result.shape:
        result = cv2.resize(result, (original.shape[1], original.shape[0]))
    
    # 计算差异
    diff = cv2.absdiff(original, result)
    diff_gray = cv2.cvtColor(diff, cv2.COLOR_BGR2GRAY)
    
    # 计算质量指标
    mse = np.mean(diff_gray ** 2)
    psnr = 20 * np.log10(255.0 / np.sqrt(mse)) if mse > 0 else float('inf')
    
    # 计算修改区域比例
    threshold = 10  # 差异阈值
    changed_pixels = np.sum(diff_gray > threshold)
    total_pixels = diff_gray.shape[0] * diff_gray.shape[1]
    change_ratio = changed_pixels / total_pixels
    
    return {
        'mse': mse,
        'psnr': psnr,
        'change_ratio': change_ratio
    }

def main():
    # 定义测试图片和结果路径
    test_images = [
        'test_images/abskoop_watermark_test.jpg',
        'test_images/angled_watermark.jpg', 
        'test_images/light_watermark.jpg',
        'test_images/dense_watermark.jpg'
    ]
    
    # 定义不同方法的结果
    methods = {
        '基础版本': 'test_results_advanced/basic',
        '改进版本': 'test_results_improved', 
        '最终版本': 'test_results_final'
    }
    
    # 创建对比目录
    comparison_dir = Path('comparison_results')
    comparison_dir.mkdir(exist_ok=True)
    
    print("🔍 开始生成对比分析...")
    print("=" * 50)
    
    # 为每个测试图片生成对比
    for test_img_path in test_images:
        test_img = Path(test_img_path)
        if not test_img.exists():
            print(f"测试图片不存在: {test_img_path}")
            continue
            
        print(f"\n处理图片: {test_img.name}")
        
        # 收集不同方法的结果
        results_dict = {}
        quality_analysis = {}
        
        for method_name, result_dir in methods.items():
            # 查找对应的结果文件
            result_files = []
            
            if method_name == '基础版本':
                result_files = list(Path(result_dir).glob(f"removed_{test_img.stem}*"))
            elif method_name == '改进版本':
                result_files = list(Path(result_dir).glob(f"*{test_img.stem}*"))
            elif method_name == '最终版本':
                result_files = list(Path(result_dir).glob(f"smart_{test_img.stem}*"))
            
            if result_files:
                result_path = result_files[0]  # 取第一个匹配的文件
                results_dict[method_name] = result_path
                
                # 分析质量
                quality = analyze_watermark_removal_quality(test_img_path, result_path)
                if quality:
                    quality_analysis[method_name] = quality
                    print(f"  {method_name}: PSNR={quality['psnr']:.2f}, 修改比例={quality['change_ratio']:.2%}")
            else:
                print(f"  {method_name}: 未找到结果文件")
        
        # 生成对比图
        if results_dict:
            comparison_output = comparison_dir / f"comparison_{test_img.stem}.png"
            create_comparison_grid(test_img_path, results_dict, comparison_output)
    
    print(f"\n✅ 对比分析完成！结果保存在: {comparison_dir}")
    print("\n📊 总结:")
    print("- 基础版本: 使用简单的亮度检测和OpenCV修复")
    print("- 改进版本: 多种检测方法组合，更精确的遮罩")  
    print("- 最终版本: 智能自适应检测，根据图像特征调整参数")
    print("\n建议查看生成的对比图来评估不同方法的效果。")

if __name__ == "__main__":
    main()
