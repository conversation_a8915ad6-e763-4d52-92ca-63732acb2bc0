#!/usr/bin/env python3
"""
安装依赖并运行倾斜水印检测工具
"""

import subprocess
import sys
import os
from pathlib import Path

def install_requirements():
    """安装必要的依赖"""
    print("🔧 安装必要的依赖...")
    
    requirements = [
        "opencv-python>=4.8.0",
        "numpy>=1.21.0",
        "Pillow>=8.0.0"
    ]
    
    for req in requirements:
        try:
            print(f"安装 {req}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", req])
            print(f"✅ {req} 安装成功")
        except subprocess.CalledProcessError as e:
            print(f"❌ {req} 安装失败: {e}")
            return False
    
    return True

def run_watermark_detection():
    """运行水印检测"""
    print("\n🎯 开始运行倾斜水印检测...")
    
    input_file = "input_images/lee_f5d42015103a8772117281383f5c0948.jpg"
    output_file = "output_images/lee_rotated_removed.jpg"
    
    if not Path(input_file).exists():
        print(f"❌ 输入文件不存在: {input_file}")
        return False
    
    # 确保输出目录存在
    Path(output_file).parent.mkdir(exist_ok=True)
    
    try:
        cmd = [
            sys.executable, 
            "rotated_watermark_detector.py",
            "--input", input_file,
            "--output", output_file,
            "--debug"
        ]
        
        subprocess.check_call(cmd)
        print(f"✅ 处理完成！结果保存在: {output_file}")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 处理失败: {e}")
        return False

def main():
    print("🚀 倾斜水印去除工具")
    print("=" * 50)
    
    # 1. 安装依赖
    if not install_requirements():
        print("❌ 依赖安装失败，请手动安装")
        return
    
    # 2. 运行检测
    if run_watermark_detection():
        print("\n🎉 成功完成！")
        print("📁 生成的文件:")
        print("  - output_images/lee_rotated_removed.jpg (去水印结果)")
        print("  - output_images/debug/lee_rotated_removed_rotated_mask.png (检测遮罩)")
        print("  - output_images/debug/lee_rotated_removed_rotated_visualization.jpg (可视化)")
    else:
        print("\n❌ 处理失败")

if __name__ == "__main__":
    main()
