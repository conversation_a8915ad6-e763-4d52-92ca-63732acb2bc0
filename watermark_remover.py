#!/usr/bin/env python3
"""
倾斜重复水印去除工具
专门处理 WWW.ABSKOOP.COM 等倾斜、重复的水印
"""

import cv2
import numpy as np
import pytesseract
from PIL import Image, ImageDraw, ImageFont
import argparse
import os
import logging
from typing import List, Tuple, Optional
import re

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RotatedWatermarkRemover:
    def __init__(self):
        self.target_texts = [
            "WWW.ABSKOOP.COM",
            "ABSKOOP.COM", 
            "WWW.ABSKOOP",
            "ABSKOOP"
        ]
        
    def detect_watermarks_ocr(self, image_path: str) -> List[dict]:
        """
        使用OCR检测倾斜的水印文字
        """
        logger.info("🔍 开始OCR检测水印...")
        
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"无法读取图像: {image_path}")
            
        # 转换为RGB
        rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # 使用pytesseract检测文字，包括角度信息
        try:
            # 获取详细的OCR数据，包括角度
            data = pytesseract.image_to_data(rgb_image, output_type=pytesseract.Output.DICT, 
                                           config='--psm 6 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZ./')
            
            watermark_regions = []
            
            # 分析OCR结果
            for i in range(len(data['text'])):
                text = data['text'][i].strip()
                confidence = int(data['conf'][i])
                
                # 检查是否包含目标水印文字
                if confidence > 30 and text:  # 降低置信度要求
                    for target in self.target_texts:
                        if target.lower() in text.lower() or self._fuzzy_match(text, target):
                            x, y, w, h = data['left'][i], data['top'][i], data['width'][i], data['height'][i]
                            
                            if w > 10 and h > 5:  # 过滤太小的区域
                                watermark_regions.append({
                                    'bbox': (x, y, x+w, y+h),
                                    'text': text,
                                    'confidence': confidence,
                                    'angle': 0  # OCR暂时不提供角度，后续可以改进
                                })
                                logger.info(f"发现水印: '{text}' at ({x},{y},{x+w},{y+h}) 置信度: {confidence}")
            
            logger.info(f"OCR检测到 {len(watermark_regions)} 个水印区域")
            return watermark_regions
            
        except Exception as e:
            logger.error(f"OCR检测失败: {e}")
            return []
    
    def _fuzzy_match(self, text: str, target: str) -> bool:
        """
        模糊匹配，处理OCR识别错误
        """
        # 移除特殊字符，只保留字母
        clean_text = re.sub(r'[^A-Za-z]', '', text.upper())
        clean_target = re.sub(r'[^A-Za-z]', '', target.upper())
        
        if len(clean_text) < 3:
            return False
            
        # 检查是否包含目标的主要部分
        if "ABSKOOP" in clean_text or "ABSKOP" in clean_text:
            return True
            
        return False
    
    def detect_watermarks_template(self, image_path: str) -> List[dict]:
        """
        使用模板匹配检测重复的水印模式
        """
        logger.info("🔍 开始模板匹配检测...")
        
        image = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
        if image is None:
            return []
        
        # 预处理图像
        # 增强对比度
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        enhanced = clahe.apply(image)
        
        # 检测可能的水印区域（浅色区域）
        # 水印通常比背景稍浅
        _, binary = cv2.threshold(enhanced, 200, 255, cv2.THRESH_BINARY)
        
        # 形态学操作，连接相近的区域
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
        binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
        
        # 查找轮廓
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        watermark_regions = []
        
        for contour in contours:
            # 获取边界框
            x, y, w, h = cv2.boundingRect(contour)
            
            # 过滤尺寸：水印通常有特定的尺寸范围
            if 50 < w < 300 and 15 < h < 60:
                # 检查宽高比：水印通常是横向的
                aspect_ratio = w / h
                if 2 < aspect_ratio < 15:
                    # 提取区域进行进一步分析
                    roi = enhanced[y:y+h, x:x+w]
                    
                    # 检查区域的特征
                    mean_val = np.mean(roi)
                    std_val = np.std(roi)
                    
                    # 水印区域通常有特定的灰度特征
                    if 180 < mean_val < 250 and 5 < std_val < 30:
                        watermark_regions.append({
                            'bbox': (x, y, x+w, y+h),
                            'text': 'detected_pattern',
                            'confidence': 80,
                            'angle': 0
                        })
        
        logger.info(f"模板匹配检测到 {len(watermark_regions)} 个水印区域")
        return watermark_regions
    
    def merge_detections(self, ocr_regions: List[dict], template_regions: List[dict]) -> List[dict]:
        """
        合并OCR和模板匹配的结果
        """
        all_regions = ocr_regions + template_regions
        
        if not all_regions:
            return []
        
        # 去重：合并重叠的区域
        merged_regions = []
        
        for region in all_regions:
            bbox = region['bbox']
            is_duplicate = False
            
            for existing in merged_regions:
                if self._boxes_overlap(bbox, existing['bbox']):
                    is_duplicate = True
                    # 保留置信度更高的
                    if region['confidence'] > existing['confidence']:
                        merged_regions.remove(existing)
                        merged_regions.append(region)
                    break
            
            if not is_duplicate:
                merged_regions.append(region)
        
        logger.info(f"合并后共 {len(merged_regions)} 个水印区域")
        return merged_regions
    
    def _boxes_overlap(self, box1: Tuple[int, int, int, int], box2: Tuple[int, int, int, int]) -> bool:
        """
        检查两个边界框是否重叠
        """
        x1_1, y1_1, x2_1, y2_1 = box1
        x1_2, y1_2, x2_2, y2_2 = box2
        
        # 计算重叠面积
        overlap_x = max(0, min(x2_1, x2_2) - max(x1_1, x1_2))
        overlap_y = max(0, min(y2_1, y2_2) - max(y1_1, y1_2))
        overlap_area = overlap_x * overlap_y
        
        # 计算两个框的面积
        area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
        area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
        
        # 如果重叠面积超过较小框的50%，认为是重叠
        min_area = min(area1, area2)
        return overlap_area > 0.5 * min_area
    
    def remove_watermarks(self, image_path: str, watermark_regions: List[dict], output_path: str):
        """
        去除检测到的水印
        """
        logger.info("🎨 开始去除水印...")
        
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"无法读取图像: {image_path}")
        
        # 创建掩码
        mask = np.zeros(image.shape[:2], dtype=np.uint8)
        
        for region in watermark_regions:
            x1, y1, x2, y2 = region['bbox']
            
            # 扩展边界框，确保完全覆盖水印
            padding = 5
            x1 = max(0, x1 - padding)
            y1 = max(0, y1 - padding)
            x2 = min(image.shape[1], x2 + padding)
            y2 = min(image.shape[0], y2 + padding)
            
            # 在掩码上标记水印区域
            mask[y1:y2, x1:x2] = 255
        
        # 使用OpenCV的inpainting进行修复
        # 尝试两种不同的修复算法
        try:
            # 方法1: TELEA算法
            result = cv2.inpaint(image, mask, 3, cv2.INPAINT_TELEA)
            
            # 如果效果不好，尝试方法2: NS算法
            # result = cv2.inpaint(image, mask, 3, cv2.INPAINT_NS)
            
        except Exception as e:
            logger.error(f"修复失败: {e}")
            result = image
        
        # 保存结果
        cv2.imwrite(output_path, result)
        logger.info(f"✅ 水印去除完成，保存到: {output_path}")
    
    def create_debug_image(self, image_path: str, watermark_regions: List[dict], output_path: str):
        """
        创建调试图像，显示检测到的水印区域
        """
        image = cv2.imread(image_path)
        if image is None:
            return
        
        # 在图像上绘制检测到的区域
        for i, region in enumerate(watermark_regions):
            x1, y1, x2, y2 = region['bbox']
            
            # 绘制边界框
            cv2.rectangle(image, (x1, y1), (x2, y2), (0, 255, 0), 2)
            
            # 添加标签
            label = f"{i+1}: {region['text'][:10]}..."
            cv2.putText(image, label, (x1, y1-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
        
        cv2.imwrite(output_path, image)
        logger.info(f"调试图像保存到: {output_path}")

def main():
    parser = argparse.ArgumentParser(description='倾斜重复水印去除工具')
    parser.add_argument('--input', '-i', required=True, help='输入图像路径')
    parser.add_argument('--output', '-o', required=True, help='输出图像路径')
    parser.add_argument('--debug', action='store_true', help='生成调试图像')
    
    args = parser.parse_args()
    
    # 检查输入文件
    if not os.path.exists(args.input):
        logger.error(f"输入文件不存在: {args.input}")
        return
    
    # 创建输出目录
    os.makedirs(os.path.dirname(args.output), exist_ok=True)
    
    # 初始化水印去除器
    remover = RotatedWatermarkRemover()
    
    try:
        # 检测水印
        logger.info("🚀 开始水印检测...")
        
        # 使用OCR检测
        ocr_regions = remover.detect_watermarks_ocr(args.input)
        
        # 使用模板匹配检测
        template_regions = remover.detect_watermarks_template(args.input)
        
        # 合并检测结果
        all_regions = remover.merge_detections(ocr_regions, template_regions)
        
        if not all_regions:
            logger.warning("⚠️ 未检测到水印区域")
            return
        
        logger.info(f"✅ 总共检测到 {len(all_regions)} 个水印区域")
        
        # 生成调试图像
        if args.debug:
            debug_path = args.output.replace('.jpg', '_debug.jpg').replace('.png', '_debug.png')
            remover.create_debug_image(args.input, all_regions, debug_path)
        
        # 去除水印
        remover.remove_watermarks(args.input, all_regions, args.output)
        
        logger.info("🎉 水印去除完成！")
        
    except Exception as e:
        logger.error(f"❌ 处理失败: {e}")

if __name__ == "__main__":
    main()
