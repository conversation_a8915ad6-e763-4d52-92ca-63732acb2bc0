#!/usr/bin/env python3
"""
基于OCR的精确水印去除工具
只去除特定的水印文字，保护正常内容
"""

import os
import sys
from pathlib import Path
import logging
import re

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

try:
    from PIL import Image, ImageFilter, ImageDraw
    import numpy as np
except ImportError:
    logger.error("需要安装 Pillow 和 numpy: pip install Pillow numpy")
    sys.exit(1)

try:
    import pytesseract
    OCR_AVAILABLE = True
except ImportError:
    OCR_AVAILABLE = False
    logger.warning("OCR功能不可用，需要安装: pip install pytesseract")

class OCRWatermarkRemover:
    def __init__(self):
        self.watermark_patterns = [
            r'WWW\.ABSKOOP\.COM',
            r'ABSKOOP\.COM',
            r'WWW\.ABSKOOP',
            r'ABSKOOP'
        ]
        
    def detect_watermark_with_ocr(self, image_path):
        """
        使用OCR检测特定水印文字
        """
        if not OCR_AVAILABLE:
            logger.error("OCR不可用，使用备用检测方法")
            return self._detect_watermark_fallback(image_path)
        
        try:
            with Image.open(image_path) as img:
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # 使用OCR检测文字
                ocr_data = pytesseract.image_to_data(img, output_type=pytesseract.Output.DICT)
                
                watermark_regions = []
                
                # 检查每个检测到的文字
                for i in range(len(ocr_data['text'])):
                    text = ocr_data['text'][i].strip()
                    confidence = int(ocr_data['conf'][i])
                    
                    if confidence > 30 and text:  # 只考虑置信度较高的文字
                        # 检查是否匹配水印模式
                        for pattern in self.watermark_patterns:
                            if re.search(pattern, text.upper()):
                                x = ocr_data['left'][i]
                                y = ocr_data['top'][i]
                                w = ocr_data['width'][i]
                                h = ocr_data['height'][i]
                                
                                # 扩展区域以确保完全覆盖
                                margin = 10
                                x1 = max(0, x - margin)
                                y1 = max(0, y - margin)
                                x2 = x + w + margin
                                y2 = y + h + margin
                                
                                watermark_regions.append((x1, y1, x2, y2))
                                logger.info(f"检测到水印文字: '{text}' 位置({x}, {y}, {w}, {h})")
                                break
                
                logger.info(f"OCR检测到 {len(watermark_regions)} 个水印区域")
                return watermark_regions
                
        except Exception as e:
            logger.error(f"OCR检测失败: {e}")
            return self._detect_watermark_fallback(image_path)
    
    def _detect_watermark_fallback(self, image_path):
        """
        备用检测方法：基于模板匹配
        """
        logger.info("使用备用检测方法")
        
        try:
            with Image.open(image_path) as img:
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                img_array = np.array(img)
                height, width = img_array.shape[:2]
                
                # 转换为灰度
                gray = np.dot(img_array[...,:3], [0.2989, 0.5870, 0.1140])
                
                regions = []
                
                # 更精确的检测参数
                block_size = 120  # 增大块大小以匹配水印文字长度
                step = 60
                
                for y in range(0, height - block_size, step):
                    for x in range(0, width - block_size, step):
                        block = gray[y:y+block_size, x:x+block_size]
                        
                        mean_brightness = np.mean(block)
                        std_brightness = np.std(block)
                        
                        # 检测水印特征 - 更严格的条件
                        if (190 < mean_brightness < 220 and  # 特定的灰度范围
                            8 < std_brightness < 20 and     # 适中的对比度
                            self._has_text_pattern(block)):  # 文字模式检测
                            
                            regions.append((x, y, x + block_size, y + block_size))
                
                logger.info(f"备用方法检测到 {len(regions)} 个潜在水印区域")
                return regions
                
        except Exception as e:
            logger.error(f"备用检测失败: {e}")
            return []
    
    def _has_text_pattern(self, block):
        """
        检测是否有文字模式
        """
        # 计算水平和垂直的投影
        h_projection = np.sum(block < np.mean(block), axis=1)
        v_projection = np.sum(block < np.mean(block), axis=0)
        
        # 文字通常有规律的水平和垂直分布
        h_variance = np.var(h_projection)
        v_variance = np.var(v_projection)
        
        # 如果投影有一定的变化，可能是文字
        return h_variance > 10 and v_variance > 10
    
    def remove_watermark_precise(self, image_path, output_path, regions):
        """
        精确去除水印，保护其他内容
        """
        try:
            with Image.open(image_path) as img:
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                logger.info(f"开始精确处理 {len(regions)} 个水印区域")
                
                for i, region in enumerate(regions):
                    x1, y1, x2, y2 = region
                    
                    # 提取水印区域
                    roi = img.crop((x1, y1, x2, y2))
                    
                    # 温和的处理 - 只是轻微模糊和调整
                    # 1. 轻微模糊
                    blurred_roi = roi.filter(ImageFilter.GaussianBlur(radius=1.5))
                    
                    # 2. 轻微降低对比度
                    from PIL import ImageEnhance
                    enhancer = ImageEnhance.Contrast(blurred_roi)
                    low_contrast = enhancer.enhance(0.8)  # 轻微降低
                    
                    # 3. 轻微提亮
                    brightness_enhancer = ImageEnhance.Brightness(low_contrast)
                    final_roi = brightness_enhancer.enhance(1.05)  # 轻微提亮
                    
                    # 将处理后的区域粘贴回去
                    img.paste(final_roi, (x1, y1))
                    
                    logger.info(f"处理水印区域 {i+1}/{len(regions)}: ({x1}, {y1}, {x2}, {y2})")
                
                # 保存结果 - 保持高质量
                img.save(output_path, quality=98)
                return True
                
        except Exception as e:
            logger.error(f"精确修复失败 {image_path}: {e}")
            return False
    
    def process_single_image(self, input_path, output_path):
        """
        处理单张图片
        """
        try:
            logger.info(f"处理: {input_path}")
            
            # 检测水印区域
            regions = self.detect_watermark_with_ocr(input_path)
            
            if not regions:
                logger.info(f"未检测到目标水印，复制原图: {input_path}")
                import shutil
                shutil.copy2(input_path, output_path)
                return True
            
            # 精确去除水印
            success = self.remove_watermark_precise(input_path, output_path, regions)
            
            if success:
                logger.info(f"处理完成: {output_path}")
            
            return success
            
        except Exception as e:
            logger.error(f"处理失败 {input_path}: {e}")
            return False

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='基于OCR的精确水印去除工具')
    parser.add_argument('--input', '-i', required=True, help='输入目录或文件')
    parser.add_argument('--output', '-o', required=True, help='输出目录或文件')
    
    args = parser.parse_args()
    
    remover = OCRWatermarkRemover()
    
    logger.info("🎯 基于OCR的精确水印去除工具启动")
    logger.info("=" * 50)
    
    input_path = Path(args.input)
    output_path = Path(args.output)
    
    if input_path.is_file():
        output_path.parent.mkdir(parents=True, exist_ok=True)
        remover.process_single_image(input_path, output_path)
    else:
        output_path.mkdir(parents=True, exist_ok=True)
        
        supported_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
        image_files = []
        for ext in supported_formats:
            image_files.extend(input_path.glob(f"*{ext}"))
            image_files.extend(input_path.glob(f"*{ext.upper()}"))
        
        if not image_files:
            logger.error(f"在 {args.input} 中未找到支持的图片文件")
            return
        
        logger.info(f"找到 {len(image_files)} 个图片文件")
        
        for img_file in image_files:
            output_file = output_path / img_file.name
            remover.process_single_image(img_file, output_file)

if __name__ == "__main__":
    main()
